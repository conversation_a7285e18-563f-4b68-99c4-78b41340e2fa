<?php
/**
 * WPFM Converter Class
 * Handles bidirectional conversion between Gutenberg blocks and WordPress Flavored Markdown
 */

class WPFM_Converter {
    
    private $block_patterns;
    private $markdown_patterns;
    
    public function __construct() {
        $this->init_patterns();
    }
    
    private function init_patterns() {
        // Block to Markdown conversion patterns
        $this->block_patterns = array(
            // Core text blocks
            'core/heading' => array($this, 'convert_heading_to_markdown'),
            'core/paragraph' => array($this, 'convert_paragraph_to_markdown'),
            'core/quote' => array($this, 'convert_quote_to_markdown'),
            'core/pullquote' => array($this, 'convert_pullquote_to_markdown'),
            'core/list' => array($this, 'convert_list_to_markdown'),
            'core/code' => array($this, 'convert_code_to_markdown'),
            'core/preformatted' => array($this, 'convert_preformatted_to_markdown'),
            'core/verse' => array($this, 'convert_verse_to_markdown'),
            
            // Media blocks
            'core/image' => array($this, 'convert_image_to_markdown'),
            'core/gallery' => array($this, 'convert_gallery_to_markdown'),
            'core/audio' => array($this, 'convert_audio_to_markdown'),
            'core/video' => array($this, 'convert_video_to_markdown'),
            
            // Layout blocks
            'core/columns' => array($this, 'convert_columns_to_markdown'),
            'core/column' => array($this, 'convert_column_to_markdown'),
            'core/group' => array($this, 'convert_group_to_markdown'),
            'core/cover' => array($this, 'convert_cover_to_markdown'),
            'core/spacer' => array($this, 'convert_spacer_to_markdown'),
            'core/separator' => array($this, 'convert_separator_to_markdown'),
            
            // Interactive blocks
            'core/button' => array($this, 'convert_button_to_markdown'),
            'core/buttons' => array($this, 'convert_buttons_to_markdown'),
            
            // Embed blocks
            'core/embed' => array($this, 'convert_embed_to_markdown'),
            'core-embed/youtube' => array($this, 'convert_youtube_to_markdown'),
            'core-embed/twitter' => array($this, 'convert_twitter_to_markdown'),
            
            // Table
            'core/table' => array($this, 'convert_table_to_markdown'),
            
            // HTML
            'core/html' => array($this, 'convert_html_to_markdown'),
            
            // Shortcode
            'core/shortcode' => array($this, 'convert_shortcode_to_markdown'),
        );
        
        // Markdown to Block conversion patterns
        $this->markdown_patterns = array(
            // Headers
            '/^(#{1,6})\s+(.+)$/m' => array($this, 'convert_markdown_heading'),
            
            // Block containers
            '/^:::([\w-]+)(\{[^}]*\})?\s*\n(.*?)\n:::$/ms' => array($this, 'convert_markdown_container'),
            
            // Inline containers
            '/^::([\w-]+)(\{[^}]*\})?\s*\n?(.*?)\n?::$/ms' => array($this, 'convert_markdown_inline_container'),
            
            // Images with attributes
            '/!\[([^\]]*)\]\(([^)]+)\)(\{[^}]*\})?/' => array($this, 'convert_markdown_image'),
            
            // Links with button class
            '/\[([^\]]+)\]\(([^)]+)\)(\{[^}]*\.wp-button[^}]*\})/' => array($this, 'convert_markdown_button'),
            
            // Code blocks with language
            '/^```(\w+)?(\{[^}]*\})?\s*\n(.*?)\n```$/ms' => array($this, 'convert_markdown_code'),
            
            // Blockquotes
            '/^>\s(.+)$/m' => array($this, 'convert_markdown_quote'),
            
            // Lists
            '/^(\s*)[-*+]\s(.+)$/m' => array($this, 'convert_markdown_list'),
            '/^(\s*)(\d+)\.\s(.+)$/m' => array($this, 'convert_markdown_ordered_list'),
            
            // Tables
            '/^\|(.+)\|\s*\n\|[-:\s\|]+\|\s*\n((?:\|.*\|\s*\n?)*)/m' => array($this, 'convert_markdown_table'),
            
            // Horizontal rules
            '/^---+$/m' => array($this, 'convert_markdown_separator'),
        );
    }
    
    /**
     * Convert Gutenberg blocks to WordPress Flavored Markdown
     */
    public function blocks_to_markdown($content) {
        if (empty($content)) {
            return '';
        }
        
        // Parse blocks
        $blocks = parse_blocks($content);
        $markdown = '';
        
        foreach ($blocks as $block) {
            $markdown .= $this->block_to_markdown($block) . "\n\n";
        }
        
        return trim($markdown);
    }
    
    /**
     * Convert WordPress Flavored Markdown to Gutenberg blocks
     */
    public function markdown_to_blocks($markdown) {
        if (empty($markdown)) {
            return '';
        }
        
        // Process markdown with patterns
        $blocks = array();
        $lines = explode("\n", $markdown);
        $current_block = '';
        $i = 0;
        
        while ($i < count($lines)) {
            $line = $lines[$i];
            
            // Check for block patterns
            $processed = false;
            foreach ($this->markdown_patterns as $pattern => $callback) {
                if (preg_match($pattern, $line, $matches)) {
                    // Convert to block
                    if (!empty($current_block)) {
                        $blocks[] = $this->create_paragraph_block(trim($current_block));
                        $current_block = '';
                    }
                    
                    $block_data = call_user_func($callback, $matches, $lines, $i);
                    if ($block_data) {
                        $blocks[] = $block_data['block'];
                        $i = $block_data['next_line'] ?? $i;
                        $processed = true;
                        break;
                    }
                }
            }
            
            if (!$processed) {
                if (trim($line) === '') {
                    if (!empty($current_block)) {
                        $blocks[] = $this->create_paragraph_block(trim($current_block));
                        $current_block = '';
                    }
                } else {
                    $current_block .= ($current_block ? "\n" : '') . $line;
                }
            }
            
            $i++;
        }
        
        // Handle remaining content
        if (!empty($current_block)) {
            $blocks[] = $this->create_paragraph_block(trim($current_block));
        }
        
        return serialize_blocks($blocks);
    }
    
    /**
     * Check if content appears to be markdown
     */
    public function is_markdown($content) {
        // Look for markdown indicators
        $indicators = array(
            '/^#{1,6}\s/',           // Headers
            '/^:::[a-z]/',           // Block containers
            '/^::[a-z]/',            // Inline containers
            '/^\s*[-*+]\s/',         // Lists
            '/^\s*\d+\.\s/',         // Numbered lists
            '/^>\s/',                // Blockquotes
            '/^```/',                // Code blocks
            '/^\|.*\|.*\|/',         // Tables
        );
        
        foreach ($indicators as $pattern) {
            if (preg_match($pattern . 'm', $content)) {
                return true;
            }
        }
        
        return false;
    }
    
    // =================== BLOCK TO MARKDOWN CONVERTERS ===================
    
    private function block_to_markdown($block) {
        $block_name = $block['blockName'];
        
        if (isset($this->block_patterns[$block_name])) {
            return call_user_func($this->block_patterns[$block_name], $block);
        }
        
        // Handle inner blocks
        $markdown = '';
        if (!empty($block['innerBlocks'])) {
            foreach ($block['innerBlocks'] as $inner_block) {
                $markdown .= $this->block_to_markdown($inner_block) . "\n";
            }
        }
        
        // Fallback to HTML comment
        if (empty($markdown) && !empty($block['innerHTML'])) {
            return "::html\n" . $block['innerHTML'] . "\n::";
        }
        
        return $markdown;
    }
    
    private function convert_heading_to_markdown($block) {
        $level = $block['attrs']['level'] ?? 1;
        $content = strip_tags($block['innerHTML']);
        return str_repeat('#', $level) . ' ' . $content;
    }
    
    private function convert_paragraph_to_markdown($block) {
        return strip_tags($block['innerHTML']);
    }
    
    private function convert_quote_to_markdown($block) {
        $content = strip_tags($block['innerHTML']);
        $lines = explode("\n", $content);
        $quoted = array_map(function($line) {
            return '> ' . $line;
        }, $lines);
        
        $attrs = $this->format_attributes($block['attrs']);
        return implode("\n", $quoted) . ($attrs ? "\n{.wp-quote" . $attrs . "}" : '');
    }
    
    private function convert_pullquote_to_markdown($block) {
        $content = strip_tags($block['innerHTML']);
        $attrs = $this->format_attributes($block['attrs']);
        return ":::pullquote" . $attrs . "\n" . $content . "\n:::";
    }
    
    private function convert_list_to_markdown($block) {
        $items = $block['innerBlocks'] ?? array();
        $ordered = $block['attrs']['ordered'] ?? false;
        $markdown = '';
        
        foreach ($items as $i => $item) {
            $content = strip_tags($item['innerHTML']);
            if ($ordered) {
                $markdown .= ($i + 1) . '. ' . $content . "\n";
            } else {
                $markdown .= '- ' . $content . "\n";
            }
        }
        
        return trim($markdown);
    }
    
    private function convert_code_to_markdown($block) {
        $language = $block['attrs']['language'] ?? '';
        $content = strip_tags($block['innerHTML']);
        $attrs = $this->format_attributes($block['attrs'], array('language'));
        
        return "```" . $language . $attrs . "\n" . $content . "\n```";
    }
    
    private function convert_image_to_markdown($block) {
        $src = $block['attrs']['url'] ?? '';
        $alt = $block['attrs']['alt'] ?? '';
        $caption = $block['attrs']['caption'] ?? '';
        
        $attrs = $this->format_attributes($block['attrs'], array('url', 'alt', 'caption'));
        if ($caption) {
            $attrs .= ' caption="' . esc_attr($caption) . '"';
        }
        
        return "![" . $alt . "](" . $src . ")" . ($attrs ? "{" . $attrs . "}" : '');
    }
    
    private function convert_gallery_to_markdown($block) {
        $images = $block['attrs']['images'] ?? array();
        $attrs = $this->format_attributes($block['attrs'], array('images'));
        
        $markdown = ":::gallery" . $attrs . "\n";
        foreach ($images as $image) {
            $markdown .= "![" . ($image['alt'] ?? '') . "](" . $image['fullUrl'] . ")\n";
        }
        $markdown .= ":::";
        
        return $markdown;
    }
    
    private function convert_columns_to_markdown($block) {
        $columns = $block['innerBlocks'] ?? array();
        $attrs = $this->format_attributes($block['attrs']);
        
        $markdown = ":::columns" . $attrs . "\n";
        foreach ($columns as $column) {
            $markdown .= "::column\n";
            $markdown .= $this->block_to_markdown($column);
            $markdown .= "\n::\n\n";
        }
        $markdown .= ":::";
        
        return $markdown;
    }
    
    private function convert_button_to_markdown($block) {
        $text = strip_tags($block['innerHTML']);
        $url = $block['attrs']['url'] ?? '#';
        $attrs = $this->format_attributes($block['attrs'], array('url'));
        
        return "[" . $text . "](" . $url . "){.wp-button" . $attrs . "}";
    }
    
    private function convert_embed_to_markdown($block) {
        $provider = $block['attrs']['providerNameSlug'] ?? 'generic';
        $url = $block['attrs']['url'] ?? '';
        $attrs = $this->format_attributes($block['attrs'], array('url', 'providerNameSlug'));
        
        return "::" . $provider . "{url=\"" . $url . "\"" . $attrs . "}\n::";
    }
    
    private function convert_table_to_markdown($block) {
        $body = $block['attrs']['body'] ?? array();
        $head = $block['attrs']['head'] ?? array();
        $attrs = $this->format_attributes($block['attrs'], array('body', 'head'));
        
        $markdown = '';
        
        // Header
        if (!empty($head)) {
            $markdown .= '| ' . implode(' | ', $head[0]['cells']) . ' |';
            if ($attrs) {
                $markdown .= '{.wp-table' . $attrs . '}';
            }
            $markdown .= "\n";
            $markdown .= '|' . str_repeat('-----|', count($head[0]['cells'])) . "\n";
        }
        
        // Body
        foreach ($body as $row) {
            $markdown .= '| ' . implode(' | ', $row['cells']) . ' |' . "\n";
        }
        
        return trim($markdown);
    }
    
    private function convert_spacer_to_markdown($block) {
        $height = $block['attrs']['height'] ?? 50;
        return ":::spacer{height=" . $height . "}\n:::";
    }
    
    private function convert_separator_to_markdown($block) {
        $attrs = $this->format_attributes($block['attrs']);
        if ($attrs) {
            return "::separator" . $attrs . "\n::";
        }
        return "---";
    }
    
    private function convert_html_to_markdown($block) {
        return "::html\n" . $block['innerHTML'] . "\n::";
    }
    
    private function convert_shortcode_to_markdown($block) {
        return "::shortcode\n" . $block['innerHTML'] . "\n::";
    }
    
    // =================== MARKDOWN TO BLOCK CONVERTERS ===================
    
    private function convert_markdown_heading($matches) {
        $level = strlen($matches[1]);
        $content = trim($matches[2]);
        
        return array(
            'block' => array(
                'blockName' => 'core/heading',
                'attrs' => array('level' => $level),
                'innerBlocks' => array(),
                'innerHTML' => '<h' . $level . '>' . $content . '</h' . $level . '>',
                'innerContent' => array('<h' . $level . '>' . $content . '</h' . $level . '>')
            )
        );
    }
    
    private function convert_markdown_container($matches) {
        $container_type = $matches[1];
        $attributes = $this->parse_attributes($matches[2] ?? '');
        $content = $matches[3];
        
        switch ($container_type) {
            case 'columns':
                return $this->create_columns_block($content, $attributes);
            case 'gallery':
                return $this->create_gallery_block($content, $attributes);
            case 'pullquote':
                return $this->create_pullquote_block($content, $attributes);
            case 'group':
                return $this->create_group_block($content, $attributes);
            case 'cover':
                return $this->create_cover_block($content, $attributes);
            case 'spacer':
                return $this->create_spacer_block($attributes);
            default:
                return $this->create_html_block("<!-- " . $container_type . " -->\n" . $content);
        }
    }
    
    private function convert_markdown_image($matches) {
        $alt = $matches[1];
        $src = $matches[2];
        $attributes = $this->parse_attributes($matches[3] ?? '');
        
        return array(
            'block' => array(
                'blockName' => 'core/image',
                'attrs' => array_merge(
                    array('url' => $src, 'alt' => $alt),
                    $attributes
                ),
                'innerBlocks' => array(),
                'innerHTML' => '<figure class="wp-block-image"><img src="' . esc_url($src) . '" alt="' . esc_attr($alt) . '"/></figure>',
                'innerContent' => array('<figure class="wp-block-image"><img src="' . esc_url($src) . '" alt="' . esc_attr($alt) . '"/></figure>')
            )
        );
    }
    
    private function convert_markdown_button($matches) {
        $text = $matches[1];
        $url = $matches[2];
        $attributes = $this->parse_attributes($matches[3] ?? '');
        
        return array(
            'block' => array(
                'blockName' => 'core/button',
                'attrs' => array_merge(
                    array('url' => $url, 'text' => $text),
                    $attributes
                ),
                'innerBlocks' => array(),
                'innerHTML' => '<div class="wp-block-button"><a class="wp-block-button__link" href="' . esc_url($url) . '">' . esc_html($text) . '</a></div>',
                'innerContent' => array('<div class="wp-block-button"><a class="wp-block-button__link" href="' . esc_url($url) . '">' . esc_html($text) . '</a></div>')
            )
        );
    }
    
    private function convert_markdown_code($matches) {
        $language = $matches[1] ?? '';
        $attributes = $this->parse_attributes($matches[2] ?? '');
        $code = $matches[3];
        
        return array(
            'block' => array(
                'blockName' => 'core/code',
                'attrs' => array_merge(
                    array('language' => $language),
                    $attributes
                ),
                'innerBlocks' => array(),
                'innerHTML' => '<pre class="wp-block-code"><code>' . esc_html($code) . '</code></pre>',
                'innerContent' => array('<pre class="wp-block-code"><code>' . esc_html($code) . '</code></pre>')
            )
        );
    }
    
    // =================== HELPER METHODS ===================
    
    private function format_attributes($attrs, $exclude = array()) {
        if (empty($attrs)) {
            return '';
        }
        
        $formatted = array();
        foreach ($attrs as $key => $value) {
            if (!in_array($key, $exclude)) {
                if (is_bool($value)) {
                    $formatted[] = $key . '=' . ($value ? 'true' : 'false');
                } else {
                    $formatted[] = $key . '="' . esc_attr($value) . '"';
                }
            }
        }
        
        return empty($formatted) ? '' : ' ' . implode(' ', $formatted);
    }
    
    private function parse_attributes($attr_string) {
        $attributes = array();
        if (preg_match('/\{([^}]+)\}/', $attr_string, $matches)) {
            $attr_parts = explode(' ', $matches[1]);
            foreach ($attr_parts as $part) {
                if (strpos($part, '=') !== false) {
                    list($key, $value) = explode('=', $part, 2);
                    $value = trim($value, '"\'');
                    $attributes[trim($key)] = $value;
                }
            }
        }
        return $attributes;
    }
    
    private function create_paragraph_block($content) {
        return array(
            'blockName' => 'core/paragraph',
            'attrs' => array(),
            'innerBlocks' => array(),
            'innerHTML' => '<p>' . $content . '</p>',
            'innerContent' => array('<p>' . $content . '</p>')
        );
    }
    
    private function create_columns_block($content, $attributes) {
        // Parse column content
        $column_parts = explode('::column', $content);
        $columns = array();
        
        foreach ($column_parts as $part) {
            if (trim($part)) {
                $column_content = trim(str_replace('::', '', $part));
                if ($column_content) {
                    $columns[] = array(
                        'blockName' => 'core/column',
                        'attrs' => array(),
                        'innerBlocks' => array($this->create_paragraph_block($column_content)),
                        'innerHTML' => '',
                        'innerContent' => array(null)
                    );
                }
            }
        }
        
        return array(
            'block' => array(
                'blockName' => 'core/columns',
                'attrs' => $attributes,
                'innerBlocks' => $columns,
                'innerHTML' => '',
                'innerContent' => array_fill(0, count($columns), null)
            )
        );
    }
    
    private function create_html_block($content) {
        return array(
            'block' => array(
                'blockName' => 'core/html',
                'attrs' => array(),
                'innerBlocks' => array(),
                'innerHTML' => $content,
                'innerContent' => array($content)
            )
        );
    }
    
    private function create_spacer_block($attributes) {
        return array(
            'block' => array(
                'blockName' => 'core/spacer',
                'attrs' => $attributes,
                'innerBlocks' => array(),
                'innerHTML' => '<div style="height:' . ($attributes['height'] ?? 50) . 'px" aria-hidden="true" class="wp-block-spacer"></div>',
                'innerContent' => array('<div style="height:' . ($attributes['height'] ?? 50) . 'px" aria-hidden="true" class="wp-block-spacer"></div>')
            )
        );
    }
}
?>