<?php
/**
 * Plugin Name: WordPress Flavored Markdown (WPFM)
 * Plugin URI: https://example.com/wpfm
 * Description: Store, edit, and version WordPress content using an extended markdown format that maps to Gutenberg blocks
 * Version: 1.0.0
 * Author: <PERSON> 
 * License: GPL v2 or later
 * Requires PHP: 7.4
 * Requires at least: 5.8
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WPFM_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WPFM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WPFM_VERSION', '1.0.0');

// Main plugin class
class WordPressFlavoreMarkdown {
    
    private static $instance = null;
    private $converter;
    private $storage;
    private $editor;
    private $revisions;
    
    public static function getInstance() {
        if (self::$instance == null) {
            self::$instance = new WordPressFlavoreMarkdown();
        }
        return self::$instance;
    }
    
    private function __construct() {
        $this->init();
    }
    
    private function init() {
        // Load dependencies
        require_once WPFM_PLUGIN_DIR . 'includes/class-wpfm-converter.php';
        require_once WPFM_PLUGIN_DIR . 'includes/class-wpfm-storage.php';
        require_once WPFM_PLUGIN_DIR . 'includes/class-wpfm-editor.php';
        require_once WPFM_PLUGIN_DIR . 'includes/class-wpfm-revisions.php';
        
        $this->converter = new WPFM_Converter();
        $this->storage = new WPFM_Storage();
        $this->editor = new WPFM_Editor();
        $this->revisions = new WPFM_Revisions();
        
        // Hooks
        add_action('init', array($this, 'init_hooks'));
        add_action('enqueue_block_editor_assets', array($this, 'enqueue_editor_assets'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Content filters
        add_filter('wp_insert_post_data', array($this, 'convert_blocks_to_markdown'), 10, 2);
        add_filter('the_content', array($this, 'convert_markdown_to_blocks'), 5);
        add_filter('get_edit_post_link', array($this, 'maybe_redirect_to_markdown_editor'), 10, 3);
        
        // AJAX handlers
        add_action('wp_ajax_wpfm_save_markdown', array($this, 'ajax_save_markdown'));
        add_action('wp_ajax_wpfm_load_markdown', array($this, 'ajax_load_markdown'));
        add_action('wp_ajax_wpfm_convert_blocks', array($this, 'ajax_convert_blocks'));
        
        // Add custom post meta
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
    }
    
    public function init_hooks() {
        // Register custom tables on init
        $this->storage->create_tables();
    }
    
    public function enqueue_editor_assets() {
        // Enqueue markdown editor assets
        wp_enqueue_script(
            'wpfm-editor',
            WPFM_PLUGIN_URL . 'assets/js/editor.js',
            array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components'),
            WPFM_VERSION,
            true
        );
        
        wp_enqueue_style(
            'wpfm-editor',
            WPFM_PLUGIN_URL . 'assets/css/editor.css',
            array('wp-edit-blocks'),
            WPFM_VERSION
        );
        
        wp_localize_script('wpfm-editor', 'wpfm_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wpfm_nonce'),
            'post_id' => get_the_ID()
        ));
    }
    
    public function add_admin_menu() {
        add_options_page(
            'WPFM Settings',
            'Markdown Settings',
            'manage_options',
            'wpfm-settings',
            array($this, 'admin_page')
        );
    }
    
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1>WordPress Flavored Markdown Settings</h1>
            <form method="post" action="options.php">
                <?php
                settings_fields('wpfm_settings');
                do_settings_sections('wpfm_settings');
                ?>
                <table class="form-table">
                    <tr>
                        <th scope="row">Enable for Post Types</th>
                        <td>
                            <?php
                            $post_types = get_post_types(array('public' => true), 'objects');
                            $enabled_types = get_option('wpfm_enabled_post_types', array('post', 'page'));
                            foreach ($post_types as $post_type) {
                                $checked = in_array($post_type->name, $enabled_types) ? 'checked' : '';
                                echo '<label><input type="checkbox" name="wpfm_enabled_post_types[]" value="' . $post_type->name . '" ' . $checked . '> ' . $post_type->label . '</label><br>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Storage Mode</th>
                        <td>
                            <select name="wpfm_storage_mode">
                                <option value="dual" <?php selected(get_option('wpfm_storage_mode', 'dual'), 'dual'); ?>>Dual Storage (Blocks + Markdown)</option>
                                <option value="markdown_only" <?php selected(get_option('wpfm_storage_mode'), 'markdown_only'); ?>>Markdown Only</option>
                            </select>
                            <p class="description">Dual storage keeps both formats for compatibility.</p>
                        </td>
                    </tr>
                </table>
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }
    
    public function convert_blocks_to_markdown($data, $postarr) {
        if (!$this->should_process_post_type($data['post_type'])) {
            return $data;
        }
        
        // Convert Gutenberg blocks to WPFM
        if (!empty($data['post_content']) && has_blocks($data['post_content'])) {
            $markdown = $this->converter->blocks_to_markdown($data['post_content']);
            
            // Store markdown version
            $this->storage->save_markdown($postarr['ID'] ?? 0, $markdown, 'auto');
            
            // Optionally replace post_content with markdown
            if (get_option('wpfm_storage_mode') === 'markdown_only') {
                $data['post_content'] = $markdown;
            }
        }
        
        return $data;
    }
    
    public function convert_markdown_to_blocks($content) {
        global $post;
        
        if (!$this->should_process_post_type($post->post_type ?? '')) {
            return $content;
        }
        
        // Check if content is markdown
        if ($this->converter->is_markdown($content)) {
            return $this->converter->markdown_to_blocks($content);
        }
        
        return $content;
    }
    
    public function add_meta_boxes() {
        $post_types = get_option('wpfm_enabled_post_types', array('post', 'page'));
        
        foreach ($post_types as $post_type) {
            add_meta_box(
                'wpfm_markdown_editor',
                'Markdown Editor',
                array($this, 'markdown_editor_meta_box'),
                $post_type,
                'normal',
                'high'
            );
            
            add_meta_box(
                'wpfm_revisions',
                'Markdown Revisions',
                array($this, 'revisions_meta_box'),
                $post_type,
                'side',
                'default'
            );
        }
    }
    
    public function markdown_editor_meta_box($post) {
        $this->editor->render_editor_interface($post);
    }
    
    public function revisions_meta_box($post) {
        $revisions = $this->storage->get_revisions($post->ID, 10);
        ?>
        <div id="wpfm-revisions-list">
            <?php if (empty($revisions)): ?>
                <p>No markdown revisions found.</p>
            <?php else: ?>
                <ul>
                    <?php foreach ($revisions as $revision): ?>
                        <li>
                            <a href="#" class="wpfm-load-revision" data-revision-id="<?php echo $revision->id; ?>">
                                <?php echo date('M j, Y H:i', strtotime($revision->created_at)); ?>
                                <span class="revision-type">(<?php echo $revision->revision_type; ?>)</span>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            <?php endif; ?>
        </div>
        <?php
    }
    
    public function ajax_save_markdown() {
        check_ajax_referer('wpfm_nonce', 'nonce');
        
        $post_id = intval($_POST['post_id']);
        $markdown = sanitize_textarea_field($_POST['markdown']);
        $type = sanitize_text_field($_POST['type'] ?? 'manual');
        
        if (!current_user_can('edit_post', $post_id)) {
            wp_send_json_error('Permission denied');
        }
        
        // Save markdown
        $revision_id = $this->storage->save_markdown($post_id, $markdown, $type);
        
        // Optionally update post content with converted blocks
        if (get_option('wpfm_storage_mode') !== 'markdown_only') {
            $blocks = $this->converter->markdown_to_blocks($markdown);
            wp_update_post(array(
                'ID' => $post_id,
                'post_content' => $blocks
            ));
        }
        
        wp_send_json_success(array(
            'revision_id' => $revision_id,
            'message' => 'Markdown saved successfully'
        ));
    }
    
    public function ajax_load_markdown() {
        check_ajax_referer('wpfm_nonce', 'nonce');
        
        $post_id = intval($_POST['post_id']);
        $revision_id = intval($_POST['revision_id'] ?? 0);
        
        if (!current_user_can('read_post', $post_id)) {
            wp_send_json_error('Permission denied');
        }
        
        if ($revision_id) {
            $markdown = $this->storage->get_revision($revision_id);
        } else {
            $markdown = $this->storage->get_latest_markdown($post_id);
        }
        
        wp_send_json_success(array(
            'markdown' => $markdown
        ));
    }
    
    public function ajax_convert_blocks() {
        check_ajax_referer('wpfm_nonce', 'nonce');
        
        $post_id = intval($_POST['post_id']);
        $direction = sanitize_text_field($_POST['direction']); // 'to_markdown' or 'to_blocks'
        $content = wp_unslash($_POST['content']);
        
        if (!current_user_can('edit_post', $post_id)) {
            wp_send_json_error('Permission denied');
        }
        
        if ($direction === 'to_markdown') {
            $result = $this->converter->blocks_to_markdown($content);
        } else {
            $result = $this->converter->markdown_to_blocks($content);
        }
        
        wp_send_json_success(array(
            'content' => $result
        ));
    }
    
    private function should_process_post_type($post_type) {
        $enabled_types = get_option('wpfm_enabled_post_types', array('post', 'page'));
        return in_array($post_type, $enabled_types);
    }
}

// Initialize plugin
add_action('plugins_loaded', function() {
    WordPressFlavoreMarkdown::getInstance();
});

// Activation hook
register_activation_hook(__FILE__, function() {
    $storage = new WPFM_Storage();
    $storage->create_tables();
});
?>