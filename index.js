/**
 * WordPress Flavored Markdown Editor
 * Handle the markdown editor interface and conversions
 */

(function($) {
    'use strict';
    
    class WPFMEditor {
        constructor() {
            this.currentTab = 'markdown';
            this.postId = wpfm_ajax.post_id;
            this.editor = null;
            this.previewContainer = null;
            this.blocksContainer = null;
            this.autoSaveInterval = null;
            
            this.init();
        }
        
        init() {
            // Initialize editor when DOM is ready
            $(document).ready(() => {
                this.initializeEditor();
                this.bindEvents();
                this.startAutoSave();
            });
        }
        
        initializeEditor() {
            // Initialize CodeMirror or similar for better markdown editing
            const textarea = document.getElementById('wpfm-markdown-content');
            if (textarea && typeof CodeMirror !== 'undefined') {
                this.editor = CodeMirror.fromTextArea(textarea, {
                    mode: 'markdown',
                    theme: 'default',
                    lineNumbers: true,
                    lineWrapping: true,
                    extraKeys: {
                        'Ctrl-S': () => this.saveMarkdown('manual'),
                        'Cmd-S': () => this.saveMarkdown('manual'),
                        'Ctrl-P': () => this.togglePreview(),
                        'Cmd-P': () => this.togglePreview(),
                    },
                    hintOptions: {
                        hint: this.getMarkdownHints.bind(this)
                    }
                });
                
                // Update textarea when editor changes
                this.editor.on('change', () => {
                    this.editor.save();
                });
            }
            
            this.previewContainer = document.getElementById('wpfm-preview-content');
            this.blocksContainer = document.getElementById('wpfm-blocks-content');
        }
        
        bindEvents() {
            // Tab switching
            $('.wpfm-tab').on('click', (e) => {
                const tab = $(e.target).data('tab');
                this.switchTab(tab);
            });
            
            // Toolbar buttons
            $('#wpfm-toggle-preview').on('click', () => this.togglePreview());
            $('#wpfm-sync-blocks').on('click', () => this.syncFromBlocks());
            $('#wpfm-save-markdown').on('click', () => this.saveMarkdown('manual'));
            
            // Revision loading
            $('.wpfm-load-revision').on('click', (e) => {
                e.preventDefault();
                const revisionId = $(e.target).data('revision-id');
                this.loadRevision(revisionId);
            });
            
            // Keyboard shortcuts
            $(document).on('keydown', (e) => {
                if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                    e.preventDefault();
                    this.saveMarkdown('manual');
                }
            });
        }
        
        switchTab(tab) {
            this.currentTab = tab;
            
            // Update active tab
            $('.wpfm-tab').removeClass('active');
            $(`.wpfm-tab[data-tab="${tab}"]`).addClass('active');
            
            // Update visible pane
            $('.wpfm-editor-pane').removeClass('active');
            $(`#wpfm-${tab}-pane`).addClass('active');
            
            // Load content for the tab
            switch (tab) {
                case 'preview':
                    this.updatePreview();
                    break;
                case 'blocks':
                    this.updateBlocksView();
                    break;
                case 'markdown':
                    if (this.editor) {
                        this.editor.refresh();
                    }
                    break;
            }
        }
        
        getCurrentMarkdown() {
            if (this.editor) {
                return this.editor.getValue();
            }
            return $('#wpfm-markdown-content').val();
        }
        
        setMarkdown(content) {
            if (this.editor) {
                this.editor.setValue(content);
            } else {
                $('#wpfm-markdown-content').val(content);
            }
        }
        
        saveMarkdown(type = 'manual') {
            const markdown = this.getCurrentMarkdown();
            
            if (!markdown.trim()) {
                this.showNotification('Cannot save empty content', 'warning');
                return;
            }
            
            // Show saving indicator
            const $button = $('#wpfm-save-markdown');
            const originalText = $button.text();
            $button.text('Saving...').prop('disabled', true);
            
            $.post(wpfm_ajax.ajax_url, {
                action: 'wpfm_save_markdown',
                nonce: wpfm_ajax.nonce,
                post_id: this.postId,
                markdown: markdown,
                type: type
            }, (response) => {
                if (response.success) {
                    this.showNotification('Markdown saved successfully', 'success');
                    
                    // Update revision list if visible
                    this.updateRevisionsList();
                    
                    // Update blocks view if active
                    if (this.currentTab === 'blocks') {
                        this.updateBlocksView();
                    }
                } else {
                    this.showNotification('Failed to save: ' + response.data, 'error');
                }
            }).fail(() => {
                this.showNotification('Failed to save markdown', 'error');
            }).always(() => {
                $button.text(originalText).prop('disabled', false);
            });
        }
        
        loadRevision(revisionId) {
            $.post(wpfm_ajax.ajax_url, {
                action: 'wpfm_load_markdown',
                nonce: wpfm_ajax.nonce,
                post_id: this.postId,
                revision_id: revisionId
            }, (response) => {
                if (response.success) {
                    this.setMarkdown(response.data.markdown);
                    this.showNotification('Revision loaded', 'success');
                } else {
                    this.showNotification('Failed to load revision', 'error');
                }
            });
        }
        
        syncFromBlocks() {
            // Get current post content (blocks) and convert to markdown
            const blockContent = wp.data.select('core/editor').getEditedPostContent();
            
            if (!blockContent) {
                this.showNotification('No blocks found to sync', 'warning');
                return;
            }
            
            $.post(wpfm_ajax.ajax_url, {
                action: 'wpfm_convert_blocks',
                nonce: wpfm_ajax.nonce,
                post_id: this.postId,
                direction: 'to_markdown',
                content: blockContent
            }, (response) => {
                if (response.success) {
                    this.setMarkdown(response.data.content);
                    this.showNotification('Synced from blocks', 'success');
                } else {
                    this.showNotification('Failed to sync from blocks', 'error');
                }
            });
        }
        
        updatePreview() {
            const markdown = this.getCurrentMarkdown();
            
            if (!markdown.trim()) {
                this.previewContainer.innerHTML = '<p class="wpfm-empty">No content to preview</p>';
                return;
            }
            
            // Convert markdown to HTML for preview
            $.post(wpfm_ajax.ajax_url, {
                action: 'wpfm_convert_blocks',
                nonce: wpfm_ajax.nonce,
                post_id: this.postId,
                direction: 'to_blocks',
                content: markdown
            }, (response) => {
                if (response.success) {
                    // Parse blocks and render as HTML preview
                    this.renderBlocksPreview(response.data.content);
                } else {
                    this.previewContainer.innerHTML = '<p class="wpfm-error">Preview failed to load</p>';
                }
            });
        }
        
        updateBlocksView() {
            const markdown = this.getCurrentMarkdown();
            
            if (!markdown.trim()) {
                this.blocksContainer.innerHTML = '<p class="wpfm-empty">No content to show</p>';
                return;
            }
            
            // Convert to blocks and display structure
            $.post(wpfm_ajax.ajax_url, {
                action: 'wpfm_convert_blocks',
                nonce: wpfm_ajax.nonce,
                post_id: this.postId,
                direction: 'to_blocks',
                content: markdown
            }, (response) => {
                if (response.success) {
                    this.renderBlocksStructure(response.data.content);
                } else {
                    this.blocksContainer.innerHTML = '<p class="wpfm-error">Failed to convert to blocks</p>';
                }
            });
        }
        
        renderBlocksPreview(blockContent) {
            // This would typically render the blocks as they would appear on the frontend
            // For now, we'll show the HTML representation
            this.previewContainer.innerHTML = `
                <div class="wpfm-preview">
                    ${blockContent}
                </div>
            `;
        }
        
        renderBlocksStructure(blockContent) {
            try {
                // Parse the serialized blocks
                const blocks = this.parseBlocks(blockContent);
                let html = '<div class="wpfm-blocks-structure">';
                
                blocks.forEach((block, index) => {
                    html += this.renderBlockInfo(block, 0);
                });
                
                html += '</div>';
                this.blocksContainer.innerHTML = html;
            } catch (e) {
                this.blocksContainer.innerHTML = '<p class="wpfm-error">Failed to parse blocks</p>';
            }
        }
        
        renderBlockInfo(block, depth) {
            const indent = '  '.repeat(depth);
            let html = `
                <div class="wpfm-block-info" style="margin-left: ${depth * 20}px;">
                    <div class="wpfm-block-header">
                        <strong>${block.blockName || 'Unknown'}</strong>
                        ${Object.keys(block.attrs || {}).length > 0 ? 
                            `<small>(${Object.keys(block.attrs).join(', ')})</small>` : ''}
                    </div>
            `;
            
            if (block.innerBlocks && block.innerBlocks.length > 0) {
                html += '<div class="wpfm-inner-blocks">';
                block.innerBlocks.forEach(innerBlock => {
                    html += this.renderBlockInfo(innerBlock, depth + 1);
                });
                html += '</div>';
            }
            
            html += '</div>';
            return html;
        }
        
        parseBlocks(content) {
            // Simple block parser - in reality you'd use the WordPress parse_blocks equivalent
            const blocks = [];
            const blockRegex = /<!-- wp:([^\s]+)(?:\s+(.+?))?\s*(?:\/)?-->/g;
            let match;
            
            while ((match = blockRegex.exec(content)) !== null) {
                try {
                    const blockName = match[1];
                    const attrs = match[2] ? JSON.parse(match[2]) : {};
                    blocks.push({
                        blockName: blockName,
                        attrs: attrs,
                        innerBlocks: []
                    });
                } catch (e) {
                    // Skip malformed blocks
                }
            }
            
            return blocks;
        }
        
        updateRevisionsList() {
            // Reload the revisions meta box
            $.post(wpfm_ajax.ajax_url, {
                action: 'wpfm_get_revisions',
                nonce: wpfm_ajax.nonce,
                post_id: this.postId
            }, (response) => {
                if (response.success) {
                    $('#wpfm-revisions-list').html(response.data);
                }
            });
        }
        
        togglePreview() {
            if (this.currentTab === 'preview') {
                this.switchTab('markdown');
            } else {
                this.switchTab('preview');
            }
        }
        
        startAutoSave() {
            // Auto-save every 30 seconds
            this.autoSaveInterval = setInterval(() => {
                const markdown = this.getCurrentMarkdown();
                if (markdown.trim() && this.hasUnsavedChanges()) {
                    this.saveMarkdown('auto');
                }
            }, 30000);
        }
        
        hasUnsavedChanges() {
            // Simple change detection - could be more sophisticated
            const current = this.getCurrentMarkdown();
            const saved = this.lastSavedContent || '';
            return current !== saved;
        }
        
        showNotification(message, type = 'info') {
            // Create or update notification
            let $notification = $('.wpfm-notification');
            if ($notification.length === 0) {
                $notification = $('<div class="wpfm-notification"></div>');
                $('#wpfm-editor-container').prepend($notification);
            }
            
            $notification.removeClass('success error warning info')
                        .addClass(type)
                        .text(message)
                        .fadeIn();
            
            // Auto-hide after 3 seconds
            setTimeout(() => {
                $notification.fadeOut();
            }, 3000);
        }
        
        getMarkdownHints(editor, options) {
            // Provide autocomplete hints for WPFM syntax
            const cursor = editor.getCursor();
            const line = editor.getLine(cursor.line);
            const start = cursor.ch;
            
            const hints = [];
            
            // Block container hints
            if (line.startsWith(':::')) {
                hints.push(
                    ':::columns',
                    ':::gallery',
                    ':::pullquote',
                    ':::group',
                    ':::cover',
                    ':::spacer',
                    ':::alert'
                );
            }
            
            // Inline container hints
            if (line.startsWith('::')) {
                hints.push(
                    '::youtube',
                    '::twitter',
                    '::instagram',
                    '::shortcode',
                    '::html',
                    '::separator',
                    '::video',
                    '::audio'
                );
            }
            
            // Attribute hints
            if (line.includes('{') && !line.includes('}')) {
                hints.push(
                    'className="custom-class"',
                    'id="element-id"',
                    'style="custom-style"',
                    'align="center"',
                    'width="500"',
                    'height="300"',
                    'columns="2"',
                    'gap="normal"'
                );
            }
            
            return {
                list: hints,
                from: CodeMirror.Pos(cursor.line, start),
                to: CodeMirror.Pos(cursor.line, cursor.ch)
            };
        }
        
        insertMarkdownTemplate(template) {
            const templates = {
                'columns': ':::columns{columns=2 gap=normal}\n::column\nLeft column content\n::\n\n::column\nRight column content\n::\n:::',
                'gallery': ':::gallery{columns=3 crop=true}\n![Image 1](image1.jpg)\n![Image 2](image2.jpg)\n![Image 3](image3.jpg)\n:::',
                'button': '[Button Text](https://example.com){.wp-button style=fill color=primary}',
                'alert': ':::alert{type=info}\nThis is an informational message.\n:::',
                'youtube': '::youtube{id="VIDEO_ID"}\n::',
                'code': '```php\n// Your code here\nfunction example() {\n    return "Hello World";\n}\n```',
                'table': '| Header 1 | Header 2 | Header 3 |\n|----------|----------|----------|\n| Row 1 Col 1 | Row 1 Col 2 | Row 1 Col 3 |\n| Row 2 Col 1 | Row 2 Col 2 | Row 2 Col 3 |'
            };
            
            const content = templates[template];
            if (content) {
                if (this.editor) {
                    const cursor = this.editor.getCursor();
                    this.editor.replaceRange(content, cursor);
                } else {
                    const textarea = document.getElementById('wpfm-markdown-content');
                    const start = textarea.selectionStart;
                    const end = textarea.selectionEnd;
                    const value = textarea.value;
                    textarea.value = value.substring(0, start) + content + value.substring(end);
                    textarea.focus();
                    textarea.setSelectionRange(start + content.length, start + content.length);
                }
            }
        }
        
        exportMarkdown() {
            const markdown = this.getCurrentMarkdown();
            const blob = new Blob([markdown], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `post-${this.postId}-markdown.md`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        
        importMarkdown() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.md,.markdown,.txt';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        this.setMarkdown(e.target.result);
                        this.showNotification('Markdown imported successfully', 'success');
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }
    }
    
    // Utility functions for markdown formatting
    window.WPFMUtils = {
        formatBold: function(text) {
            return `**${text}**`;
        },
        
        formatItalic: function(text) {
            return `*${text}*`;
        },
        
        formatLink: function(text, url) {
            return `[${text}](${url || '#'})`;
        },
        
        formatImage: function(alt, src, attrs = {}) {
            let attrString = '';
            if (Object.keys(attrs).length > 0) {
                const attrParts = [];
                for (const [key, value] of Object.entries(attrs)) {
                    attrParts.push(`${key}="${value}"`);
                }
                attrString = `{${attrParts.join(' ')}}`;
            }
            return `![${alt}](${src})${attrString}`;
        },
        
        formatCodeBlock: function(code, language = '') {
            return `\`\`\`${language}\n${code}\n\`\`\``;
        },
        
        formatHeading: function(text, level = 1) {
            return `${'#'.repeat(level)} ${text}`;
        },
        
        formatList: function(items, ordered = false) {
            return items.map((item, index) => {
                const prefix = ordered ? `${index + 1}. ` : '- ';
                return `${prefix}${item}`;
            }).join('\n');
        },
        
        formatTable: function(headers, rows) {
            let table = '| ' + headers.join(' | ') + ' |\n';
            table += '|' + headers.map(() => '------').join('|') + '|\n';
            rows.forEach(row => {
                table += '| ' + row.join(' | ') + ' |\n';
            });
            return table;
        },
        
        formatContainer: function(type, content, attrs = {}) {
            let attrString = '';
            if (Object.keys(attrs).length > 0) {
                const attrParts = [];
                for (const [key, value] of Object.entries(attrs)) {
                    attrParts.push(`${key}="${value}"`);
                }
                attrString = `{${attrParts.join(' ')}}`;
            }
            return `:::${type}${attrString}\n${content}\n:::`;
        }
    };
    
    // Enhanced toolbar for markdown editor
    function createMarkdownToolbar() {
        const toolbar = $(`
            <div class="wpfm-enhanced-toolbar">
                <div class="wpfm-toolbar-group">
                    <button type="button" class="wpfm-tool-btn" data-action="bold" title="Bold">
                        <i class="fas fa-bold"></i>
                    </button>
                    <button type="button" class="wpfm-tool-btn" data-action="italic" title="Italic">
                        <i class="fas fa-italic"></i>
                    </button>
                    <button type="button" class="wpfm-tool-btn" data-action="link" title="Link">
                        <i class="fas fa-link"></i>
                    </button>
                </div>
                
                <div class="wpfm-toolbar-group">
                    <button type="button" class="wpfm-tool-btn" data-action="heading" title="Heading">
                        <i class="fas fa-heading"></i>
                    </button>
                    <button type="button" class="wpfm-tool-btn" data-action="quote" title="Quote">
                        <i class="fas fa-quote-left"></i>
                    </button>
                    <button type="button" class="wpfm-tool-btn" data-action="code" title="Code">
                        <i class="fas fa-code"></i>
                    </button>
                </div>
                
                <div class="wpfm-toolbar-group">
                    <button type="button" class="wpfm-tool-btn" data-action="list" title="List">
                        <i class="fas fa-list-ul"></i>
                    </button>
                    <button type="button" class="wpfm-tool-btn" data-action="ordered-list" title="Numbered List">
                        <i class="fas fa-list-ol"></i>
                    </button>
                    <button type="button" class="wpfm-tool-btn" data-action="table" title="Table">
                        <i class="fas fa-table"></i>
                    </button>
                </div>
                
                <div class="wpfm-toolbar-group">
                    <select class="wpfm-template-select" data-action="insert-template">
                        <option value="">Insert Template...</option>
                        <option value="columns">Columns</option>
                        <option value="gallery">Gallery</option>
                        <option value="button">Button</option>
                        <option value="alert">Alert</option>
                        <option value="youtube">YouTube</option>
                        <option value="code">Code Block</option>
                    </select>
                </div>
                
                <div class="wpfm-toolbar-group">
                    <button type="button" class="wpfm-tool-btn" data-action="export" title="Export Markdown">
                        <i class="fas fa-download"></i>
                    </button>
                    <button type="button" class="wpfm-tool-btn" data-action="import" title="Import Markdown">
                        <i class="fas fa-upload"></i>
                    </button>
                </div>
            </div>
        `);
        
        // Insert toolbar before editor
        $('#wpfm-editor-container .wpfm-toolbar').after(toolbar);
        
        // Bind toolbar events
        toolbar.on('click', '.wpfm-tool-btn', function(e) {
            e.preventDefault();
            const action = $(this).data('action');
            handleToolbarAction(action);
        });
        
        toolbar.on('change', '.wpfm-template-select', function() {
            const template = $(this).val();
            if (template) {
                window.wpfmEditor.insertMarkdownTemplate(template);
                $(this).val('');
            }
        });
    }
    
    function handleToolbarAction(action) {
        const editor = window.wpfmEditor;
        
        switch (action) {
            case 'bold':
                wrapSelection('**');
                break;
            case 'italic':
                wrapSelection('*');
                break;
            case 'link':
                const url = prompt('Enter URL:');
                if (url) {
                    wrapSelection('[', `](${url})`);
                }
                break;
            case 'heading':
                insertAtLineStart('## ');
                break;
            case 'quote':
                insertAtLineStart('> ');
                break;
            case 'code':
                wrapSelection('`');
                break;
            case 'list':
                insertAtLineStart('- ');
                break;
            case 'ordered-list':
                insertAtLineStart('1. ');
                break;
            case 'table':
                editor.insertMarkdownTemplate('table');
                break;
            case 'export':
                editor.exportMarkdown();
                break;
            case 'import':
                editor.importMarkdown();
                break;
        }
    }
    
    function wrapSelection(prefix, suffix) {
        suffix = suffix || prefix;
        const editor = window.wpfmEditor.editor;
        
        if (editor) {
            const selection = editor.getSelection();
            const replacement = prefix + selection + suffix;
            editor.replaceSelection(replacement);
        }
    }
    
    function insertAtLineStart(prefix) {
        const editor = window.wpfmEditor.editor;
        
        if (editor) {
            const cursor = editor.getCursor();
            const line = editor.getLine(cursor.line);
            
            if (line.startsWith(prefix)) {
                // Remove prefix if already present
                editor.replaceRange('', 
                    { line: cursor.line, ch: 0 },
                    { line: cursor.line, ch: prefix.length }
                );
            } else {
                // Add prefix
                editor.replaceRange(prefix, { line: cursor.line, ch: 0 });
            }
        }
    }
    
    // Initialize when document is ready
    $(document).ready(function() {
        // Only initialize if we're on a page with the markdown editor
        if ($('#wpfm-editor-container').length > 0) {
            window.wpfmEditor = new WPFMEditor();
            createMarkdownToolbar();
        }
    });
    
})(jQuery);