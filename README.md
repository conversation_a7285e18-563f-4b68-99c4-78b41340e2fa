# WordPress Flavored Markdown (WPFM)

A WordPress plugin that allows you to store, edit, and version WordPress content using an extended markdown format that maps to Gutenberg blocks.

## Features

- **Bidirectional Conversion**: Convert between Gutenberg blocks and WordPress Flavored Markdown
- **Revision System**: Track changes with automatic and manual revision saving
- **Dual Storage**: Store content in both block and markdown formats for compatibility
- **Rich Editor**: CodeMirror-powered markdown editor with syntax highlighting
- **Block Editor Integration**: Sidebar panel in Gutenberg for markdown editing
- **Extended Syntax**: Support for WordPress-specific blocks and attributes

## Installation

1. Upload the plugin files to `/wp-content/plugins/baum-wordpress-flavored-markdown/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Configure settings under Settings > Markdown Settings

## WordPress Flavored Markdown Syntax

### Standard Markdown
All standard markdown syntax is supported:
- Headers (`# ## ###`)
- Paragraphs
- **Bold** and *italic* text
- [Links](http://example.com)
- ![Images](image.jpg)
- Lists (ordered and unordered)
- `Code` and code blocks
- > Blockquotes
- Tables

### Extended Syntax for WordPress Blocks

#### Block Containers
```markdown
:::blocktype{attribute="value"}
Content goes here
:::
```

Examples:
```markdown
:::columns{columns=2}
::column
First column content
::

::column
Second column content
::
:::

:::pullquote{align="center"}
This is a pull quote
:::

:::gallery{columns=3}
![Image 1](image1.jpg)
![Image 2](image2.jpg)
![Image 3](image3.jpg)
:::
```

#### Inline Containers
```markdown
::blocktype{attribute="value"}
Content
::
```

Examples:
```markdown
::html
<div class="custom-html">Custom HTML content</div>
::

::shortcode
[contact-form-7 id="123"]
::
```

#### Buttons
```markdown
[Button Text](http://example.com){.wp-button style="fill"}
```

#### Images with Attributes
```markdown
![Alt text](image.jpg){width="300" height="200" caption="Image caption"}
```

#### Code Blocks with Language
```markdown
```php{highlight="2,4"}
<?php
echo "Hello World"; // This line is highlighted
$var = "test";
echo $var; // This line is also highlighted
```
```

## Storage Modes

### Dual Storage (Default)
- Stores both Gutenberg blocks and markdown
- Ensures compatibility with themes and plugins
- Allows seamless switching between editors

### Markdown Only
- Stores only markdown format
- Converts to blocks on display
- Smaller database footprint

## API Endpoints

The plugin provides REST API endpoints for integration:

- `GET /wp/v2/wpfm/markdown/{post_id}` - Get markdown content
- `POST /wp/v2/wpfm/markdown/{post_id}` - Save markdown content
- `POST /wp/v2/wpfm/convert` - Convert between formats

## Hooks and Filters

### Actions
- `wpfm_before_save_markdown` - Before saving markdown
- `wpfm_after_save_markdown` - After saving markdown
- `wpfm_before_convert_blocks` - Before converting blocks
- `wpfm_after_convert_blocks` - After converting blocks

### Filters
- `wpfm_enabled_post_types` - Modify enabled post types
- `wpfm_markdown_content` - Filter markdown content before saving
- `wpfm_block_content` - Filter block content before saving
- `wpfm_revision_keep_count` - Number of revisions to keep (default: 50)
- `wpfm_revision_keep_days` - Days to keep revisions (default: 365)

## Requirements

- WordPress 5.8 or higher
- PHP 7.4 or higher
- Gutenberg block editor

## Development

### File Structure
```
baum-wordpress-flavored-markdown/
├── baum-wordpress-flavored-markdown.php  # Main plugin file
├── includes/                             # Core classes
│   ├── class-wpfm-converter.php         # Block/Markdown converter
│   ├── class-wpfm-storage.php           # Database operations
│   ├── class-wpfm-editor.php            # Editor interface
│   └── class-wpfm-revisions.php         # Revision management
├── assets/                               # Frontend assets
│   ├── js/
│   │   ├── admin-editor.js               # Admin editor JavaScript
│   │   └── block-editor.js               # Gutenberg integration
│   └── css/
│       ├── admin-editor.css              # Admin editor styles
│       └── block-editor.css              # Gutenberg styles
└── README.md                             # This file
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

GPL v2 or later

## Changelog

### 1.0.0
- Initial release
- Bidirectional block/markdown conversion
- Revision system
- Admin and block editor interfaces
- Extended markdown syntax support

## Support

For support, please create an issue on the GitHub repository or contact the plugin author.
