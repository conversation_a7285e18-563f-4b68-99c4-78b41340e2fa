/**
 * WPFM Admin Editor Styles
 * Styles for the markdown editor in WordPress admin
 */

/* Loading and error states */
.wpfm-loading {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  font-style: italic;
}

.wpfm-error {
  background: #ffeaea;
  border: 1px solid #dc3232;
  color: #dc3232;
  padding: 15px;
  border-radius: 4px;
  margin: 10px 0;
}

/* CodeMirror customizations */
.CodeMirror {
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: Monaco, Consolas, 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}

.CodeMirror-focused {
  border-color: #0073aa;
  box-shadow: 0 0 0 1px #0073aa;
}

.CodeMirror-scroll {
  min-height: 400px;
}

/* Markdown syntax highlighting */
.cm-header {
  color: #0073aa;
  font-weight: bold;
}

.cm-quote {
  color: #666;
  font-style: italic;
}

.cm-link {
  color: #0073aa;
  text-decoration: underline;
}

.cm-url {
  color: #00a32a;
}

.cm-strong {
  font-weight: bold;
}

.cm-em {
  font-style: italic;
}

.cm-code {
  background: #f1f1f1;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: Monaco, Consolas, monospace;
}

/* Button enhancements */
.wpfm-btn .dashicons {
  font-size: 16px;
  width: 16px;
  height: 16px;
  vertical-align: middle;
  margin-right: 5px;
}

.wpfm-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive design */
@media (max-width: 782px) {
  .wpfm-toolbar {
    flex-direction: column;
    gap: 10px;
  }
  
  .wpfm-toolbar-group {
    justify-content: center;
  }
  
  .wpfm-editor-tabs {
    flex-wrap: wrap;
  }
  
  .wpfm-tab {
    flex: 1;
    text-align: center;
    min-width: 0;
  }
  
  .wpfm-editor-footer {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .wpfm-editor-stats {
    flex-direction: column;
    gap: 5px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .wpfm-editor-container {
    background: #1e1e1e;
    border-color: #3c3c3c;
    color: #e0e0e0;
  }
  
  .wpfm-editor-header,
  .wpfm-editor-footer {
    background: #2d2d2d;
    border-color: #3c3c3c;
  }
  
  .wpfm-tab {
    background: #2d2d2d;
    border-color: #3c3c3c;
    color: #e0e0e0;
  }
  
  .wpfm-tab:hover {
    background: #3c3c3c;
  }
  
  .wpfm-tab-active {
    background: #1e1e1e;
    border-bottom-color: #0073aa;
  }
  
  .wpfm-preview-content,
  .wpfm-blocks-content {
    background: #1e1e1e;
    border-color: #3c3c3c;
    color: #e0e0e0;
  }
  
  .wpfm-markdown-textarea {
    background: #1e1e1e;
    border-color: #3c3c3c;
    color: #e0e0e0;
  }
  
  .CodeMirror {
    background: #1e1e1e;
    color: #e0e0e0;
    border-color: #3c3c3c;
  }
  
  .CodeMirror-cursor {
    border-left-color: #e0e0e0;
  }
  
  .CodeMirror-selected {
    background: #3c3c3c;
  }
  
  .cm-header {
    color: #4fc3f7;
  }
  
  .cm-quote {
    color: #999;
  }
  
  .cm-link {
    color: #4fc3f7;
  }
  
  .cm-url {
    color: #81c784;
  }
  
  .cm-code {
    background: #3c3c3c;
    color: #e0e0e0;
  }
}

/* Print styles */
@media print {
  .wpfm-editor-header,
  .wpfm-editor-footer,
  .wpfm-toolbar,
  .wpfm-editor-tabs {
    display: none;
  }
  
  .wpfm-editor-container {
    border: none;
    box-shadow: none;
  }
  
  .wpfm-editor-pane {
    display: block !important;
  }
  
  .wpfm-preview-content {
    border: none;
    padding: 0;
  }
}

/* Accessibility improvements */
.wpfm-tab:focus {
  outline: 2px solid #0073aa;
  outline-offset: -2px;
}

.wpfm-btn:focus {
  box-shadow: 0 0 0 2px #0073aa;
}

.wpfm-modal:focus-within {
  outline: none;
}

.wpfm-modal-content:focus {
  outline: 2px solid #0073aa;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .wpfm-editor-container {
    border-width: 2px;
  }
  
  .wpfm-tab-active {
    border-bottom-width: 3px;
  }
  
  .wpfm-btn {
    border-width: 2px;
  }
  
  .CodeMirror-focused {
    border-width: 2px;
    box-shadow: 0 0 0 2px #0073aa;
  }
}

/* Animation for smooth transitions */
.wpfm-editor-pane {
  transition: opacity 0.2s ease-in-out;
}

.wpfm-tab {
  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
}

.wpfm-btn {
  transition: opacity 0.2s ease-in-out;
}

/* Custom scrollbar for webkit browsers */
.wpfm-preview-content::-webkit-scrollbar,
.wpfm-blocks-content::-webkit-scrollbar {
  width: 8px;
}

.wpfm-preview-content::-webkit-scrollbar-track,
.wpfm-blocks-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.wpfm-preview-content::-webkit-scrollbar-thumb,
.wpfm-blocks-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.wpfm-preview-content::-webkit-scrollbar-thumb:hover,
.wpfm-blocks-content::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Markdown preview content styling */
.wpfm-preview-content h1,
.wpfm-preview-content h2,
.wpfm-preview-content h3,
.wpfm-preview-content h4,
.wpfm-preview-content h5,
.wpfm-preview-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.2;
}

.wpfm-preview-content h1 {
  font-size: 2em;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.wpfm-preview-content h2 {
  font-size: 1.5em;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.3em;
}

.wpfm-preview-content p {
  margin-bottom: 1em;
  line-height: 1.6;
}

.wpfm-preview-content blockquote {
  margin: 1em 0;
  padding: 0 1em;
  border-left: 4px solid #ddd;
  color: #666;
}

.wpfm-preview-content code {
  background: #f1f1f1;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: Monaco, Consolas, monospace;
  font-size: 0.9em;
}

.wpfm-preview-content pre {
  background: #f8f8f8;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1em;
  overflow-x: auto;
  margin: 1em 0;
}

.wpfm-preview-content pre code {
  background: none;
  padding: 0;
  border-radius: 0;
}

.wpfm-preview-content ul,
.wpfm-preview-content ol {
  margin: 1em 0;
  padding-left: 2em;
}

.wpfm-preview-content li {
  margin-bottom: 0.5em;
}

.wpfm-preview-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.wpfm-preview-content th,
.wpfm-preview-content td {
  border: 1px solid #ddd;
  padding: 8px 12px;
  text-align: left;
}

.wpfm-preview-content th {
  background: #f8f8f8;
  font-weight: bold;
}

.wpfm-preview-content img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

.wpfm-preview-content hr {
  border: none;
  border-top: 1px solid #ddd;
  margin: 2em 0;
}
