/**
 * WPFM Block Editor Styles
 * Styles for the Gutenberg block editor integration
 */

/* Sidebar styles */
.wpfm-sidebar {
  padding: 16px;
}

.wpfm-controls {
  margin-bottom: 16px;
}

.wpfm-controls .components-button-group {
  display: flex;
  width: 100%;
}

.wpfm-controls .components-button-group .components-button {
  flex: 1;
  justify-content: center;
}

.wpfm-markdown-textarea {
  font-family: Monaco, Consolas, 'Courier New', monospace !important;
  font-size: 13px !important;
  line-height: 1.5 !important;
  min-height: 400px !important;
}

.wpfm-last-sync {
  font-size: 12px;
  color: #666;
  margin-top: 8px;
  margin-bottom: 0;
  font-style: italic;
}

/* Loading state */
.wpfm-sidebar .components-disabled {
  opacity: 0.6;
}

/* Button enhancements */
.wpfm-controls .components-button {
  height: 36px;
  font-size: 13px;
}

.wpfm-controls .components-button.is-primary {
  background: #0073aa;
  border-color: #0073aa;
}

.wpfm-controls .components-button.is-primary:hover {
  background: #005a87;
  border-color: #005a87;
}

.wpfm-controls .components-button.is-secondary {
  color: #0073aa;
  border-color: #0073aa;
}

.wpfm-controls .components-button.is-secondary:hover {
  color: #005a87;
  border-color: #005a87;
}

/* Responsive adjustments */
@media (max-width: 782px) {
  .wpfm-controls .components-button-group {
    flex-direction: column;
  }
  
  .wpfm-controls .components-button-group .components-button {
    margin-bottom: 8px;
  }
  
  .wpfm-markdown-textarea {
    min-height: 300px !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .wpfm-markdown-textarea {
    background: #1e1e1e !important;
    color: #e0e0e0 !important;
    border-color: #3c3c3c !important;
  }
  
  .wpfm-last-sync {
    color: #999;
  }
}

/* Focus states */
.wpfm-markdown-textarea:focus {
  border-color: #0073aa !important;
  box-shadow: 0 0 0 1px #0073aa !important;
}

/* Syntax highlighting hints */
.wpfm-markdown-textarea::placeholder {
  color: #999;
  font-style: italic;
}

/* Panel body customization */
.wpfm-sidebar .components-panel__body {
  border: none;
  margin: 0;
}

.wpfm-sidebar .components-panel__body-title {
  font-size: 14px;
  font-weight: 600;
}

/* Accessibility improvements */
.wpfm-controls .components-button:focus {
  box-shadow: 0 0 0 2px #0073aa;
  outline: none;
}

.wpfm-markdown-textarea:focus {
  outline: none;
}

/* Animation for smooth interactions */
.wpfm-controls .components-button {
  transition: all 0.2s ease-in-out;
}

.wpfm-markdown-textarea {
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

/* Custom scrollbar for the textarea */
.wpfm-markdown-textarea::-webkit-scrollbar {
  width: 8px;
}

.wpfm-markdown-textarea::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.wpfm-markdown-textarea::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.wpfm-markdown-textarea::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
