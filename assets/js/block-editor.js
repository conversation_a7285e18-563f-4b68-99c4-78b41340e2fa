/**
 * WPFM Block Editor JavaScript
 * Handles integration with the Gutenberg block editor
 */

(function() {
  'use strict';

  // Check if we're in the block editor
  if (typeof wp === 'undefined' || !wp.blocks) {
    return;
  }

  const { registerPlugin } = wp.plugins;
  const { PluginSidebar, PluginSidebarMoreMenuItem } = wp.editPost;
  const { PanelBody, TextareaControl, Button, ButtonGroup } = wp.components;
  const { useState, useEffect } = wp.element;
  const { useSelect, useDispatch } = wp.data;
  const { __ } = wp.i18n;

  // WPFM Sidebar Component
  const WPFMSidebar = () => {
    const [markdown, setMarkdown] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [lastSync, setLastSync] = useState(null);

    const { blocks, postId } = useSelect((select) => {
      const { getBlocks } = select('core/block-editor');
      const { getCurrentPostId } = select('core/editor');
      
      return {
        blocks: getBlocks(),
        postId: getCurrentPostId()
      };
    });

    const { replaceBlocks } = useDispatch('core/block-editor');

    // Load initial markdown
    useEffect(() => {
      loadMarkdown();
    }, [postId]);

    const loadMarkdown = () => {
      if (!postId) return;

      setIsLoading(true);
      
      wp.apiFetch({
        path: '/wp/v2/wpfm/markdown/' + postId,
        method: 'GET'
      }).then((response) => {
        if (response.markdown) {
          setMarkdown(response.markdown);
        }
      }).catch((error) => {
        console.error('Error loading markdown:', error);
      }).finally(() => {
        setIsLoading(false);
      });
    };

    const saveMarkdown = () => {
      if (!postId || !markdown.trim()) return;

      setIsLoading(true);

      wp.apiFetch({
        path: '/wp/v2/wpfm/markdown/' + postId,
        method: 'POST',
        data: {
          markdown: markdown,
          type: 'manual'
        }
      }).then((response) => {
        setLastSync(new Date());
        // Show success notice
        wp.data.dispatch('core/notices').createSuccessNotice(
          __('Markdown saved successfully', 'wpfm'),
          { type: 'snackbar' }
        );
      }).catch((error) => {
        console.error('Error saving markdown:', error);
        wp.data.dispatch('core/notices').createErrorNotice(
          __('Error saving markdown', 'wpfm'),
          { type: 'snackbar' }
        );
      }).finally(() => {
        setIsLoading(false);
      });
    };

    const syncFromBlocks = () => {
      if (!blocks.length) return;

      setIsLoading(true);

      // Convert current blocks to markdown
      wp.apiFetch({
        path: '/wp/v2/wpfm/convert',
        method: 'POST',
        data: {
          direction: 'to_markdown',
          content: wp.blocks.serialize(blocks)
        }
      }).then((response) => {
        if (response.content) {
          setMarkdown(response.content);
          setLastSync(new Date());
        }
      }).catch((error) => {
        console.error('Error converting blocks to markdown:', error);
        wp.data.dispatch('core/notices').createErrorNotice(
          __('Error converting blocks to markdown', 'wpfm'),
          { type: 'snackbar' }
        );
      }).finally(() => {
        setIsLoading(false);
      });
    };

    const syncToBlocks = () => {
      if (!markdown.trim()) return;

      setIsLoading(true);

      // Convert markdown to blocks
      wp.apiFetch({
        path: '/wp/v2/wpfm/convert',
        method: 'POST',
        data: {
          direction: 'to_blocks',
          content: markdown
        }
      }).then((response) => {
        if (response.content) {
          const newBlocks = wp.blocks.parse(response.content);
          replaceBlocks(blocks.map(block => block.clientId), newBlocks);
          setLastSync(new Date());
          
          wp.data.dispatch('core/notices').createSuccessNotice(
            __('Blocks updated from markdown', 'wpfm'),
            { type: 'snackbar' }
          );
        }
      }).catch((error) => {
        console.error('Error converting markdown to blocks:', error);
        wp.data.dispatch('core/notices').createErrorNotice(
          __('Error converting markdown to blocks', 'wpfm'),
          { type: 'snackbar' }
        );
      }).finally(() => {
        setIsLoading(false);
      });
    };

    return (
      <div className="wpfm-sidebar">
        <PanelBody title={__('Markdown Editor', 'wpfm')} initialOpen={true}>
          <div className="wpfm-controls">
            <ButtonGroup>
              <Button
                isPrimary
                onClick={saveMarkdown}
                disabled={isLoading || !markdown.trim()}
              >
                {isLoading ? __('Saving...', 'wpfm') : __('Save Markdown', 'wpfm')}
              </Button>
            </ButtonGroup>
            
            <ButtonGroup style={{ marginTop: '10px' }}>
              <Button
                isSecondary
                onClick={syncFromBlocks}
                disabled={isLoading}
              >
                {__('Sync from Blocks', 'wpfm')}
              </Button>
              <Button
                isSecondary
                onClick={syncToBlocks}
                disabled={isLoading || !markdown.trim()}
              >
                {__('Sync to Blocks', 'wpfm')}
              </Button>
            </ButtonGroup>
          </div>

          <TextareaControl
            label={__('Markdown Content', 'wpfm')}
            value={markdown}
            onChange={setMarkdown}
            rows={20}
            className="wpfm-markdown-textarea"
            disabled={isLoading}
          />

          {lastSync && (
            <p className="wpfm-last-sync">
              {__('Last synced:', 'wpfm')} {lastSync.toLocaleTimeString()}
            </p>
          )}
        </PanelBody>
      </div>
    );
  };

  // Register the plugin
  registerPlugin('wpfm-sidebar', {
    render: () => (
      <>
        <PluginSidebarMoreMenuItem
          target="wpfm-sidebar"
          icon="edit"
        >
          {__('Markdown Editor', 'wpfm')}
        </PluginSidebarMoreMenuItem>
        <PluginSidebar
          name="wpfm-sidebar"
          title={__('WordPress Flavored Markdown', 'wpfm')}
          icon="edit"
        >
          <WPFMSidebar />
        </PluginSidebar>
      </>
    )
  });

})();
