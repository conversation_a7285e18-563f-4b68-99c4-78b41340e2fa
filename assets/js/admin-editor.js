/**
 * WPFM Admin Editor JavaScript
 * Handles the markdown editor functionality in the WordPress admin
 */

(function($) {
  'use strict';

  var WPFM_AdminEditor = {
    
    editor: null,
    currentTab: 'markdown',
    
    init: function() {
      this.bindEvents();
      this.initCodeMirror();
      this.loadInitialContent();
    },
    
    bindEvents: function() {
      // Tab switching
      $(document).on('click', '.wpfm-tab', this.switchTab.bind(this));
      
      // Toolbar actions
      $(document).on('click', '#wpfm-save-markdown', this.saveMarkdown.bind(this));
      $(document).on('click', '#wpfm-sync-from-blocks', this.syncFromBlocks.bind(this));
      $(document).on('click', '#wpfm-sync-to-blocks', this.syncToBlocks.bind(this));
      $(document).on('click', '#wpfm-toggle-preview', this.togglePreview.bind(this));
      
      // Revision management
      $(document).on('click', '#wpfm-show-revisions', this.showRevisions.bind(this));
      $(document).on('click', '.wpfm-load-revision', this.loadRevision.bind(this));
      $(document).on('click', '.wpfm-preview-revision', this.previewRevision.bind(this));
      $(document).on('click', '.wpfm-modal-close', this.closeModal.bind(this));
      
      // Auto-save
      $(document).on('input', '#wpfm-markdown-content', this.debounce(this.autoSave.bind(this), 2000));
      
      // Keyboard shortcuts
      $(document).on('keydown', this.handleKeyboardShortcuts.bind(this));
    },
    
    initCodeMirror: function() {
      if (typeof wp.codeEditor !== 'undefined') {
        var settings = wp.codeEditor.defaultSettings ? _.clone(wp.codeEditor.defaultSettings) : {};
        settings.codemirror = _.extend({}, settings.codemirror, {
          mode: 'markdown',
          lineNumbers: true,
          lineWrapping: true,
          theme: 'default',
          extraKeys: {
            'Ctrl-S': this.saveMarkdown.bind(this),
            'Cmd-S': this.saveMarkdown.bind(this),
            'Ctrl-P': this.togglePreview.bind(this),
            'Cmd-P': this.togglePreview.bind(this)
          }
        });
        
        this.editor = wp.codeEditor.initialize($('#wpfm-markdown-content'), settings);
      }
    },
    
    loadInitialContent: function() {
      // Content is already loaded in the textarea
      // Just refresh the editor if CodeMirror is active
      if (this.editor) {
        this.editor.codemirror.refresh();
      }
    },
    
    switchTab: function(e) {
      e.preventDefault();
      
      var $tab = $(e.currentTarget);
      var tabName = $tab.data('tab');
      
      if (tabName === this.currentTab) {
        return;
      }
      
      // Update tab appearance
      $('.wpfm-tab').removeClass('wpfm-tab-active');
      $tab.addClass('wpfm-tab-active');
      
      // Switch panes
      $('.wpfm-editor-pane').removeClass('wpfm-pane-active');
      $('#wpfm-' + tabName + '-pane').addClass('wpfm-pane-active');
      
      this.currentTab = tabName;
      
      // Load content for the active tab
      if (tabName === 'preview') {
        this.updatePreview();
      } else if (tabName === 'blocks') {
        this.updateBlocksView();
      }
      
      // Refresh CodeMirror when switching back to markdown
      if (tabName === 'markdown' && this.editor) {
        setTimeout(() => {
          this.editor.codemirror.refresh();
        }, 100);
      }
    },
    
    saveMarkdown: function(e) {
      if (e) {
        e.preventDefault();
      }
      
      var markdown = this.getMarkdownContent();
      var $button = $('#wpfm-save-markdown');
      
      $button.prop('disabled', true).text(wpfm_admin.strings.saving || 'Saving...');
      
      $.ajax({
        url: wpfm_admin.ajax_url,
        type: 'POST',
        data: {
          action: 'wpfm_save_markdown',
          nonce: wpfm_admin.nonce,
          post_id: wpfm_admin.post_id,
          markdown: markdown,
          type: 'manual'
        },
        success: function(response) {
          if (response.success) {
            this.showNotice(wpfm_admin.strings.save_success, 'success');
          } else {
            this.showNotice(response.data || wpfm_admin.strings.save_error, 'error');
          }
        }.bind(this),
        error: function() {
          this.showNotice(wpfm_admin.strings.save_error, 'error');
        }.bind(this),
        complete: function() {
          $button.prop('disabled', false).html('<span class="dashicons dashicons-yes"></span> Save Draft');
        }
      });
    },
    
    syncFromBlocks: function(e) {
      e.preventDefault();
      
      var $button = $(e.currentTarget);
      $button.prop('disabled', true);
      
      $.ajax({
        url: wpfm_admin.ajax_url,
        type: 'POST',
        data: {
          action: 'wpfm_convert_blocks',
          nonce: wpfm_admin.nonce,
          post_id: wpfm_admin.post_id,
          direction: 'to_markdown',
          content: $('#content').val() // WordPress editor content
        },
        success: function(response) {
          if (response.success) {
            this.setMarkdownContent(response.data.content);
            this.showNotice(wpfm_admin.strings.sync_success, 'success');
          } else {
            this.showNotice(response.data || wpfm_admin.strings.sync_error, 'error');
          }
        }.bind(this),
        error: function() {
          this.showNotice(wpfm_admin.strings.sync_error, 'error');
        }.bind(this),
        complete: function() {
          $button.prop('disabled', false);
        }
      });
    },
    
    syncToBlocks: function(e) {
      e.preventDefault();
      
      var markdown = this.getMarkdownContent();
      var $button = $(e.currentTarget);
      $button.prop('disabled', true);
      
      $.ajax({
        url: wpfm_admin.ajax_url,
        type: 'POST',
        data: {
          action: 'wpfm_convert_blocks',
          nonce: wpfm_admin.nonce,
          post_id: wpfm_admin.post_id,
          direction: 'to_blocks',
          content: markdown
        },
        success: function(response) {
          if (response.success) {
            // Update the WordPress editor
            if (typeof tinymce !== 'undefined' && tinymce.get('content')) {
              tinymce.get('content').setContent(response.data.content);
            } else {
              $('#content').val(response.data.content);
            }
            this.showNotice(wpfm_admin.strings.sync_success, 'success');
          } else {
            this.showNotice(response.data || wpfm_admin.strings.sync_error, 'error');
          }
        }.bind(this),
        error: function() {
          this.showNotice(wpfm_admin.strings.sync_error, 'error');
        }.bind(this),
        complete: function() {
          $button.prop('disabled', false);
        }
      });
    },
    
    togglePreview: function(e) {
      if (e) {
        e.preventDefault();
      }
      
      if (this.currentTab === 'preview') {
        // Switch back to markdown
        $('.wpfm-tab[data-tab="markdown"]').click();
      } else {
        // Switch to preview
        $('.wpfm-tab[data-tab="preview"]').click();
      }
    },
    
    updatePreview: function() {
      var markdown = this.getMarkdownContent();
      var $content = $('#wpfm-preview-content');
      
      if (!markdown.trim()) {
        $content.html('<div class="wpfm-preview-placeholder"><span class="dashicons dashicons-visibility"></span><p>Enter some markdown content to see the preview.</p></div>');
        return;
      }
      
      $content.html('<div class="wpfm-loading">Loading preview...</div>');
      
      $.ajax({
        url: wpfm_admin.ajax_url,
        type: 'POST',
        data: {
          action: 'wpfm_preview_markdown',
          nonce: wpfm_admin.nonce,
          post_id: wpfm_admin.post_id,
          markdown: markdown
        },
        success: function(response) {
          if (response.success) {
            $content.html(response.data.html);
          } else {
            $content.html('<div class="wpfm-error">Error generating preview: ' + (response.data || 'Unknown error') + '</div>');
          }
        },
        error: function() {
          $content.html('<div class="wpfm-error">Error generating preview</div>');
        }
      });
    },
    
    updateBlocksView: function() {
      var markdown = this.getMarkdownContent();
      var $content = $('#wpfm-blocks-content');
      
      if (!markdown.trim()) {
        $content.html('<div class="wpfm-blocks-placeholder"><span class="dashicons dashicons-screenoptions"></span><p>Enter some markdown content to see the block representation.</p></div>');
        return;
      }
      
      $content.html('<div class="wpfm-loading">Converting to blocks...</div>');
      
      $.ajax({
        url: wpfm_admin.ajax_url,
        type: 'POST',
        data: {
          action: 'wpfm_convert_blocks',
          nonce: wpfm_admin.nonce,
          post_id: wpfm_admin.post_id,
          direction: 'to_blocks',
          content: markdown
        },
        success: function(response) {
          if (response.success) {
            $content.html('<pre><code>' + this.escapeHtml(response.data.content) + '</code></pre>');
          } else {
            $content.html('<div class="wpfm-error">Error converting to blocks: ' + (response.data || 'Unknown error') + '</div>');
          }
        }.bind(this),
        error: function() {
          $content.html('<div class="wpfm-error">Error converting to blocks</div>');
        }
      });
    },
    
    showRevisions: function(e) {
      e.preventDefault();
      $('#wpfm-revisions-modal').show();
    },
    
    closeModal: function(e) {
      e.preventDefault();
      $('.wpfm-modal').hide();
    },
    
    loadRevision: function(e) {
      e.preventDefault();
      
      if (!confirm(wpfm_admin.strings.confirm_load_revision)) {
        return;
      }
      
      var revisionId = $(e.currentTarget).data('revision-id');
      
      $.ajax({
        url: wpfm_admin.ajax_url,
        type: 'POST',
        data: {
          action: 'wpfm_load_markdown',
          nonce: wpfm_admin.nonce,
          post_id: wpfm_admin.post_id,
          revision_id: revisionId
        },
        success: function(response) {
          if (response.success) {
            this.setMarkdownContent(response.data.markdown);
            this.closeModal();
            this.showNotice('Revision loaded successfully', 'success');
          } else {
            this.showNotice(response.data || 'Error loading revision', 'error');
          }
        }.bind(this),
        error: function() {
          this.showNotice('Error loading revision', 'error');
        }.bind(this)
      });
    },
    
    previewRevision: function(e) {
      e.preventDefault();
      // TODO: Implement revision preview
    },
    
    autoSave: function() {
      // Auto-save functionality (silent save)
      var markdown = this.getMarkdownContent();
      
      $.ajax({
        url: wpfm_admin.ajax_url,
        type: 'POST',
        data: {
          action: 'wpfm_save_markdown',
          nonce: wpfm_admin.nonce,
          post_id: wpfm_admin.post_id,
          markdown: markdown,
          type: 'auto'
        }
      });
    },
    
    handleKeyboardShortcuts: function(e) {
      // Ctrl/Cmd + S to save
      if ((e.ctrlKey || e.metaKey) && e.keyCode === 83) {
        e.preventDefault();
        this.saveMarkdown();
      }
      
      // Ctrl/Cmd + P to toggle preview
      if ((e.ctrlKey || e.metaKey) && e.keyCode === 80) {
        e.preventDefault();
        this.togglePreview();
      }
    },
    
    getMarkdownContent: function() {
      if (this.editor) {
        return this.editor.codemirror.getValue();
      }
      return $('#wpfm-markdown-content').val();
    },
    
    setMarkdownContent: function(content) {
      if (this.editor) {
        this.editor.codemirror.setValue(content);
      } else {
        $('#wpfm-markdown-content').val(content);
      }
    },
    
    showNotice: function(message, type) {
      var $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
      $('.wrap h1').after($notice);
      
      setTimeout(function() {
        $notice.fadeOut();
      }, 5000);
    },
    
    escapeHtml: function(text) {
      var map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
      };
      
      return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    },
    
    debounce: function(func, wait, immediate) {
      var timeout;
      return function() {
        var context = this, args = arguments;
        var later = function() {
          timeout = null;
          if (!immediate) func.apply(context, args);
        };
        var callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
      };
    }
  };

  // Initialize when document is ready
  $(document).ready(function() {
    if ($('#wpfm-editor-container').length) {
      WPFM_AdminEditor.init();
    }
  });

})(jQuery);
