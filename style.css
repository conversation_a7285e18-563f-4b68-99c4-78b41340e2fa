/**
 * WordPress Flavored Markdown Editor Styles
 */

/* Main Editor Container */
#wpfm-editor-container {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin: 20px 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Toolbar Styles */
.wpfm-toolbar {
    background: #f9f9f9;
    border-bottom: 1px solid #ddd;
    padding: 10px;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.wpfm-enhanced-toolbar {
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
    padding: 8px 12px;
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.wpfm-toolbar-group {
    display: flex;
    gap: 5px;
    align-items: center;
    border-right: 1px solid #ddd;
    padding-right: 15px;
}

.wpfm-toolbar-group:last-child {
    border-right: none;
}

.wpfm-tool-btn {
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 3px;
    padding: 6px 8px;
    cursor: pointer;
    font-size: 14px;
    color: #555;
    transition: all 0.2s ease;
}

.wpfm-tool-btn:hover {
    background: #f0f0f0;
    border-color: #999;
    color: #333;
}

.wpfm-tool-btn:active {
    background: #e9e9e9;
    transform: translateY(1px);
}

.wpfm-template-select {
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 3px;
    padding: 6px 8px;
    font-size: 14px;
    color: #555;
    min-width: 150px;
}

/* Tab Navigation */
#wpfm-editor-tabs {
    display: flex;
    background: #f9f9f9;
    border-bottom: 1px solid #ddd;
    margin: 0;
}

.wpfm-tab {
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    background: #f9f9f9;
    color: #666;
    font-weight: 500;
    transition: all 0.2s ease;
    position: relative;
}

.wpfm-tab:hover {
    background: #f0f0f0;
    color: #333;
}

.wpfm-tab.active {
    background: #fff;
    color: #0073aa;
    border-bottom-color: #0073aa;
}

/* Editor Panes */
.wpfm-editor-pane {
    display: none;
    min-height: 400px;
}

.wpfm-editor-pane.active {
    display: block;
}

/* Markdown Editor */
#wpfm-markdown-editor {
    position: relative;
}

#wpfm-markdown-content {
    width: 100%;
    min-height: 400px;
    border: none;
    outline: none;
    resize: vertical;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.6;
    padding: 20px;
    background: #fafafa;
    color: #333;
}

/* CodeMirror Overrides */
.CodeMirror {
    border: none;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.6;
    min-height: 400px;
    background: #fafafa;
}

.CodeMirror-lines {
    padding: 20px 0;
}

.CodeMirror-line {
    padding: 0 20px;
}

.CodeMirror-cursor {
    border-left: 2px solid #0073aa;
}

.CodeMirror-selected {
    background: rgba(0, 115, 170, 0.1);
}

/* Syntax Highlighting for Markdown */
.cm-header {
    color: #0073aa;
    font-weight: bold;
}

.cm-strong {
    font-weight: bold;
    color: #d63638;
}

.cm-em {
    font-style: italic;
    color: #d63638;
}

.cm-link {
    color: #00a32a;
    text-decoration: underline;
}

.cm-code {
    background: rgba(0,0,0,0.05);
    border-radius: 3px;
    padding: 1px 3px;
    font-family: monospace;
    color: #d63638;
}

.cm-quote {
    color: #666;
    font-style: italic;
}

.cm-variable-2 {
    color: #0073aa; /* For WPFM block syntax */
}

/* Preview Pane */
#wpfm-preview-pane {
    padding: 20px;
    background: #fff;
    overflow-y: auto;
    max-height: 600px;
}

.wpfm-preview {
    max-width: none;
}

.wpfm-preview h1,
.wpfm-preview h2,
.wpfm-preview h3,
.wpfm-preview h4,
.wpfm-preview h5,
.wpfm-preview h6 {
    color: #333;
    margin: 1.2em 0 0.6em 0;
}

.wpfm-preview p {
    line-height: 1.6;
    margin: 1em 0;
}

.wpfm-preview img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
}

.wpfm-preview code {
    background: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

.wpfm-preview pre {
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    overflow-x: auto;
}

.wpfm-preview blockquote {
    border-left: 4px solid #0073aa;
    margin: 1.5em 0;
    padding: 0 0 0 15px;
    color: #666;
    font-style: italic;
}

.wpfm-preview table {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
}

.wpfm-preview th,
.wpfm-preview td {
    border: 1px solid #ddd;
    padding: 8px 12px;
    text-align: left;
}

.wpfm-preview th {
    background: #f9f9f9;
    font-weight: 600;
}

/* Blocks Structure View */
#wpfm-blocks-pane {
    padding: 20px;
    background: #fff;
    font-family: monospace;
    font-size: 13px;
}

.wpfm-blocks-structure {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    overflow-x: auto;
}

.wpfm-block-info {
    margin: 5px 0;
    padding: 5px 10px;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    position: relative;
}

.wpfm-block-info:hover {
    background: #f5f5f5;
    border-color: #ccc;
}

.wpfm-block-header {
    font-weight: 600;
    color: #0073aa;
}

.wpfm-block-header small {
    color: #666;
    font-weight: normal;
    font-size: 11px;
}

.wpfm-inner-blocks {
    margin-left: 15px;
    border-left: 2px solid #e0e0e0;
    padding-left: 10px;
    margin-top: 8px;
}

/* Notification Styles */
.wpfm-notification {
    display: none;
    padding: 10px 15px;
    margin: 0 0 15px 0;
    border-radius: 4px;
    font-weight: 500;
}

.wpfm-notification.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.wpfm-notification.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.wpfm-notification.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.wpfm-notification.info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

/* Empty States */
.wpfm-empty {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 40px 20px;
}

.wpfm-error {
    text-align: center;
    color: #d63638;
    padding: 20px;
}

/* Revisions List */
#wpfm-revisions-list {
    max-height: 300px;
    overflow-y: auto;
}

#wpfm-revisions-list ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

#wpfm-revisions-list li {
    margin: 0;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

#wpfm-revisions-list li:last-child {
    border-bottom: none;
}

.wpfm-load-revision {
    text-decoration: none;
    color: #0073aa;
    display: block;
    padding: 5px 10px;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.wpfm-load-revision:hover {
    background: #f0f8ff;
    text-decoration: none;
}

.revision-type {
    font-size: 11px;
    color: #666;
    font-weight: normal;
}

/* Responsive Design */
@media (max-width: 768px) {
    .wpfm-toolbar,
    .wpfm-enhanced-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .wpfm-toolbar-group {
        border-right: none;
        border-bottom: 1px solid #ddd;
        padding-right: 0;
        padding-bottom: 10px;
        justify-content: center;
    }
    
    .wpfm-toolbar-group:last-child {
        border-bottom: none;
        padding-bottom: 0;
    }
    
    #wpfm-editor-tabs {
        flex-wrap: wrap;
    }
    
    .wpfm-tab {
        flex: 1;
        text-align: center;
        min-width: 100px;
    }
    
    #wpfm-markdown-content,
    .CodeMirror {
        min-height: 300px;
    }
    
    .CodeMirror-line,
    #wpfm-markdown-content {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    #wpfm-preview-pane,
    #wpfm-blocks-pane {
        padding: 15px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    #wpfm-editor-container {
        background: #2c3338;
        border-color: #50575e;
        color: #f0f0f1;
    }
    
    .wpfm-toolbar,
    .wpfm-enhanced-toolbar,
    #wpfm-editor-tabs {
        background: #1d2327;
        border-color: #3c434a;
    }
    
    .wpfm-tab {
        background: #1d2327;
        color: #a7aaad;
    }
    
    .wpfm-tab.active {
        background: #2c3338;
        color: #72aee6;
        border-bottom-color: #72aee6;
    }
    
    .wpfm-tool-btn,
    .wpfm-template-select {
        background: #2c3338;
        border-color: #50575e;
        color: #a7aaad;
    }
    
    .wpfm-tool-btn:hover {
        background: #3c434a;
        border-color: #646970;
        color: #f0f0f1;
    }
    
    #wpfm-markdown-content,
    .CodeMirror {
        background: #1d2327;
        color: #f0f0f1;
    }
    
    .CodeMirror-cursor {
        border-left-color: #72aee6;
    }
    
    #wpfm-preview-pane,
    #wpfm-blocks-pane {
        background: #2c3338;
        color: #f0f0f1;
    }
    
    .wpfm-blocks-structure {
        background: #1d2327;
        border-color: #3c434a;
    }
    
    .wpfm-block-info {
        background: #2c3338;
        border-color: #50575e;
    }
}

/* Animation for smooth transitions */
.wpfm-editor-pane {
    transition: opacity 0.2s ease-in-out;
}

.wpfm-editor-pane:not(.active) {
    opacity: 0;
}

.wpfm-editor-pane.active {
    opacity: 1;
}

/* Custom scrollbars */
.wpfm-editor-pane::-webkit-scrollbar,
.CodeMirror-scroll::-webkit-scrollbar,
#wpfm-revisions-list::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.wpfm-editor-pane::-webkit-scrollbar-track,
.CodeMirror-scroll::-webkit-scrollbar-track,
#wpfm-revisions-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.wpfm-editor-pane::-webkit-scrollbar-thumb,
.CodeMirror-scroll::-webkit-scrollbar-thumb,
#wpfm-revisions-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.wpfm-editor-pane::-webkit-scrollbar-thumb:hover,
.CodeMirror-scroll::-webkit-scrollbar-thumb:hover,
#wpfm-revisions-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading States */
.wpfm-loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.wpfm-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: wpfm-spin 1s linear infinite;
}

@keyframes wpfm-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Help Text and Tooltips */
.wpfm-help-text {
    font-size: 13px;
    color: #666;
    font-style: italic;
    margin-top: 5px;
}

.wpfm-tooltip {
    position: relative;
    display: inline-block;
}

.wpfm-tooltip .wpfm-tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 12px;
    line-height: 1.4;
}

.wpfm-tooltip .wpfm-tooltiptext::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
}

.wpfm-tooltip:hover .wpfm-tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Markdown Syntax Highlighting Enhancements */
.cm-wpfm-container {
    color: #9c27b0;
    font-weight: bold;
}

.cm-wpfm-attribute {
    color: #ff9800;
}

.cm-wpfm-value {
    color: #4caf50;
}

/* Button and Form Enhancements */
.button.wpfm-primary {
    background: #0073aa;
    border-color: #005a87;
    color: #fff;
}

.button.wpfm-primary:hover {
    background: #005a87;
    border-color: #004c73;
}

.button.wpfm-secondary {
    background: #f7f7f7;
    border-color: #cccccc;
    color: #555;
}

.button.wpfm-secondary:hover {
    background: #eeeeee;
    border-color: #999999;
}

/* Split View for Side-by-Side Editing */
.wpfm-split-view {
    display: flex;
    height: 500px;
}

.wpfm-split-pane {
    flex: 1;
    overflow: hidden;
    border-right: 1px solid #ddd;
}

.wpfm-split-pane:last-child {
    border-right: none;
}

.wpfm-split-pane .wpfm-editor-pane {
    height: 100%;
    overflow-y: auto;
}

/* Advanced Toolbar Features */
.wpfm-toolbar-search {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-left: auto;
}

.wpfm-search-input {
    border: 1px solid #ccc;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 13px;
    width: 150px;
}

.wpfm-word-count {
    font-size: 12px;
    color: #666;
    white-space: nowrap;
}

/* Live Preview Styles */
.wpfm-live-preview {
    position: fixed;
    top: 32px;
    right: 0;
    width: 300px;
    height: calc(100vh - 32px);
    background: #fff;
    border-left: 1px solid #ddd;
    z-index: 9999;
    overflow-y: auto;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.wpfm-live-preview.active {
    transform: translateX(0);
}

.wpfm-live-preview-header {
    padding: 10px 15px;
    background: #f9f9f9;
    border-bottom: 1px solid #ddd;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.wpfm-live-preview-content {
    padding: 15px;
}

.wpfm-close-preview {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    color: #666;
}

/* Syntax Error Highlighting */
.wpfm-syntax-error {
    background: rgba(255, 0, 0, 0.1);
    border-bottom: 2px dotted red;
}

.wpfm-syntax-warning {
    background: rgba(255, 165, 0, 0.1);
    border-bottom: 2px dotted orange;
}

/* Full Screen Mode */
.wpfm-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    background: #fff;
}

.wpfm-fullscreen #wpfm-editor-container {
    height: 100vh;
    margin: 0;
    border-radius: 0;
    border: none;
    display: flex;
    flex-direction: column;
}

.wpfm-fullscreen .wpfm-editor-pane {
    flex: 1;
}

.wpfm-fullscreen .CodeMirror {
    height: 100%;
}

/* Export/Import Modal */
.wpfm-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wpfm-modal-content {
    background: #fff;
    border-radius: 6px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.wpfm-modal-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.wpfm-modal-title {
    margin: 0;
    font-size: 20px;
    color: #333;
}

.wpfm-modal-close {
    float: right;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #999;
    line-height: 1;
    margin-top: -5px;
}

.wpfm-modal-body {
    margin-bottom: 20px;
}

.wpfm-modal-footer {
    text-align: right;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

/* Print Styles */
@media print {
    #wpfm-editor-container {
        box-shadow: none;
        border: none;
    }
    
    .wpfm-toolbar,
    .wpfm-enhanced-toolbar,
    #wpfm-editor-tabs,
    #wpfm-revisions-list {
        display: none;
    }
    
    .wpfm-editor-pane {
        display: block !important;
        opacity: 1 !important;
    }
    
    #wpfm-preview-pane {
        padding: 0;
    }
}