<?php
/**
 * WPFM Storage Class
 * Handles markdown persistence and revisions
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

class WPFM_Storage {
  
  private $table_name;
  
  public function __construct() {
    global $wpdb;
    $this->table_name = $wpdb->prefix . 'wpfm_content';
  }
  
  /**
   * Create database tables for storing markdown content
   *
   * @return void
   */
  public function create_tables() {
    global $wpdb;
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE {$this->table_name} (
      id bigint(20) NOT NULL AUTO_INCREMENT,
      post_id bigint(20) NOT NULL,
      markdown_content longtext NOT NULL,
      revision_type varchar(20) NOT NULL DEFAULT 'manual',
      created_at datetime DEFAULT CURRENT_TIMESTAMP,
      created_by bigint(20) NOT NULL,
      PRIMARY KEY (id),
      <PERSON>EY post_id (post_id),
      <PERSON>E<PERSON> created_at (created_at)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
  }
  
  /**
   * Save markdown content with revision tracking
   *
   * @param int $post_id Post ID
   * @param string $markdown Markdown content
   * @param string $type Revision type (manual, auto, converted)
   * @return int|false Revision ID on success, false on failure
   */
  public function save_markdown($post_id, $markdown, $type = 'manual') {
    global $wpdb;
    
    // Sanitize inputs
    $post_id = intval($post_id);
    $markdown = wp_kses_post($markdown);
    $type = sanitize_text_field($type);
    
    $result = $wpdb->insert(
      $this->table_name,
      array(
        'post_id' => $post_id,
        'markdown_content' => $markdown,
        'revision_type' => $type,
        'created_by' => get_current_user_id()
      ),
      array('%d', '%s', '%s', '%d')
    );
    
    if ($result !== false) {
      // Clean up old revisions (keep last 50)
      $this->cleanup_old_revisions($post_id, 50);
      return $wpdb->insert_id;
    }
    
    return false;
  }
  
  /**
   * Get the latest markdown content for a post
   *
   * @param int $post_id Post ID
   * @return string Markdown content
   */
  public function get_latest_markdown($post_id) {
    global $wpdb;
    
    $post_id = intval($post_id);
    
    $markdown = $wpdb->get_var($wpdb->prepare(
      "SELECT markdown_content FROM {$this->table_name} 
       WHERE post_id = %d 
       ORDER BY created_at DESC 
       LIMIT 1",
      $post_id
    ));
    
    // Fallback: convert current post content if no markdown found
    if (!$markdown) {
      $post = get_post($post_id);
      if ($post && has_blocks($post->post_content)) {
        $converter = new WPFM_Converter();
        $markdown = $converter->blocks_to_markdown($post->post_content);
        $this->save_markdown($post_id, $markdown, 'converted');
      }
    }
    
    return $markdown ?: '';
  }
  
  /**
   * Get revision history for a post
   *
   * @param int $post_id Post ID
   * @param int $limit Number of revisions to retrieve
   * @return array Revision data
   */
  public function get_revisions($post_id, $limit = 10) {
    global $wpdb;
    
    $post_id = intval($post_id);
    $limit = intval($limit);
    
    return $wpdb->get_results($wpdb->prepare(
      "SELECT id, revision_type, created_at, created_by 
       FROM {$this->table_name} 
       WHERE post_id = %d 
       ORDER BY created_at DESC 
       LIMIT %d",
      $post_id,
      $limit
    ));
  }
  
  /**
   * Get a specific revision by ID
   *
   * @param int $revision_id Revision ID
   * @return string|null Markdown content or null if not found
   */
  public function get_revision($revision_id) {
    global $wpdb;
    
    $revision_id = intval($revision_id);
    
    return $wpdb->get_var($wpdb->prepare(
      "SELECT markdown_content FROM {$this->table_name} WHERE id = %d",
      $revision_id
    ));
  }
  
  /**
   * Delete a specific revision
   *
   * @param int $revision_id Revision ID
   * @return bool True on success, false on failure
   */
  public function delete_revision($revision_id) {
    global $wpdb;
    
    $revision_id = intval($revision_id);
    
    $result = $wpdb->delete(
      $this->table_name,
      array('id' => $revision_id),
      array('%d')
    );
    
    return $result !== false;
  }
  
  /**
   * Delete all revisions for a post
   *
   * @param int $post_id Post ID
   * @return bool True on success, false on failure
   */
  public function delete_post_revisions($post_id) {
    global $wpdb;
    
    $post_id = intval($post_id);
    
    $result = $wpdb->delete(
      $this->table_name,
      array('post_id' => $post_id),
      array('%d')
    );
    
    return $result !== false;
  }
  
  /**
   * Get revision statistics for a post
   *
   * @param int $post_id Post ID
   * @return array Statistics data
   */
  public function get_revision_stats($post_id) {
    global $wpdb;
    
    $post_id = intval($post_id);
    
    $stats = $wpdb->get_row($wpdb->prepare(
      "SELECT 
         COUNT(*) as total_revisions,
         MAX(created_at) as last_modified,
         MIN(created_at) as first_created
       FROM {$this->table_name} 
       WHERE post_id = %d",
      $post_id
    ), ARRAY_A);
    
    return $stats ?: array(
      'total_revisions' => 0,
      'last_modified' => null,
      'first_created' => null
    );
  }
  
  /**
   * Clean up old revisions, keeping only the most recent ones
   *
   * @param int $post_id Post ID
   * @param int $keep_count Number of revisions to keep
   * @return void
   */
  private function cleanup_old_revisions($post_id, $keep_count) {
    global $wpdb;
    
    $post_id = intval($post_id);
    $keep_count = intval($keep_count);
    
    $wpdb->query($wpdb->prepare(
      "DELETE FROM {$this->table_name} 
       WHERE post_id = %d 
       AND id NOT IN (
         SELECT id FROM (
           SELECT id FROM {$this->table_name} 
           WHERE post_id = %d 
           ORDER BY created_at DESC 
           LIMIT %d
         ) as keep_revisions
       )",
      $post_id,
      $post_id,
      $keep_count
    ));
  }
  
  /**
   * Export all markdown content for a post
   *
   * @param int $post_id Post ID
   * @return array Export data
   */
  public function export_post_markdown($post_id) {
    global $wpdb;
    
    $post_id = intval($post_id);
    
    $revisions = $wpdb->get_results($wpdb->prepare(
      "SELECT * FROM {$this->table_name} 
       WHERE post_id = %d 
       ORDER BY created_at ASC",
      $post_id
    ), ARRAY_A);
    
    return array(
      'post_id' => $post_id,
      'export_date' => current_time('mysql'),
      'revisions' => $revisions
    );
  }
  
  /**
   * Import markdown content for a post
   *
   * @param array $import_data Import data
   * @return bool True on success, false on failure
   */
  public function import_post_markdown($import_data) {
    global $wpdb;
    
    if (!isset($import_data['post_id']) || !isset($import_data['revisions'])) {
      return false;
    }
    
    $post_id = intval($import_data['post_id']);
    
    // Clear existing revisions
    $this->delete_post_revisions($post_id);
    
    // Import revisions
    foreach ($import_data['revisions'] as $revision) {
      $wpdb->insert(
        $this->table_name,
        array(
          'post_id' => $post_id,
          'markdown_content' => wp_kses_post($revision['markdown_content']),
          'revision_type' => sanitize_text_field($revision['revision_type']),
          'created_at' => sanitize_text_field($revision['created_at']),
          'created_by' => intval($revision['created_by'])
        ),
        array('%d', '%s', '%s', '%s', '%d')
      );
    }
    
    return true;
  }
}
?>
