<?php
/**
 * WPFM Revisions Class
 * Handles revision management and comparison functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

class WPFM_Revisions {
  
  private $storage;
  
  public function __construct() {
    $this->storage = new WPFM_Storage();
    
    $this->init_hooks();
  }
  
  /**
   * Initialize revision hooks
   *
   * @return void
   */
  private function init_hooks() {
    add_action('wp_ajax_wpfm_compare_revisions', array($this, 'ajax_compare_revisions'));
    add_action('wp_ajax_wpfm_restore_revision', array($this, 'ajax_restore_revision'));
    add_action('wp_ajax_wpfm_delete_revision', array($this, 'ajax_delete_revision'));
    add_action('post_updated', array($this, 'auto_save_revision'), 10, 3);
  }
  
  /**
   * Automatically save a revision when post is updated
   *
   * @param int $post_id Post ID
   * @param WP_Post $post_after Post object after update
   * @param WP_Post $post_before Post object before update
   * @return void
   */
  public function auto_save_revision($post_id, $post_after, $post_before) {
    // Check if this post type should have markdown revisions
    $enabled_types = get_option('wpfm_enabled_post_types', array('post', 'page'));
    if (!in_array($post_after->post_type, $enabled_types)) {
      return;
    }
    
    // Only save if content actually changed
    if ($post_after->post_content === $post_before->post_content) {
      return;
    }
    
    // Convert blocks to markdown and save as auto revision
    if (has_blocks($post_after->post_content)) {
      $converter = new WPFM_Converter();
      $markdown = $converter->blocks_to_markdown($post_after->post_content);
      $this->storage->save_markdown($post_id, $markdown, 'auto');
    }
  }
  
  /**
   * Compare two revisions and return diff
   *
   * @param int $revision_id_1 First revision ID
   * @param int $revision_id_2 Second revision ID
   * @return array Comparison data
   */
  public function compare_revisions($revision_id_1, $revision_id_2) {
    $revision_1 = $this->storage->get_revision($revision_id_1);
    $revision_2 = $this->storage->get_revision($revision_id_2);
    
    if (!$revision_1 || !$revision_2) {
      return array(
        'error' => __('One or both revisions not found', 'wpfm')
      );
    }
    
    // Generate line-by-line diff
    $lines_1 = explode("\n", $revision_1);
    $lines_2 = explode("\n", $revision_2);
    
    $diff = $this->generate_diff($lines_1, $lines_2);
    
    return array(
      'revision_1' => $revision_1,
      'revision_2' => $revision_2,
      'diff' => $diff,
      'stats' => array(
        'lines_added' => $diff['added_count'],
        'lines_removed' => $diff['removed_count'],
        'lines_changed' => $diff['changed_count']
      )
    );
  }
  
  /**
   * Generate a simple diff between two sets of lines
   *
   * @param array $lines_1 First set of lines
   * @param array $lines_2 Second set of lines
   * @return array Diff data
   */
  private function generate_diff($lines_1, $lines_2) {
    $diff_lines = array();
    $added_count = 0;
    $removed_count = 0;
    $changed_count = 0;
    
    $max_lines = max(count($lines_1), count($lines_2));
    
    for ($i = 0; $i < $max_lines; $i++) {
      $line_1 = isset($lines_1[$i]) ? $lines_1[$i] : null;
      $line_2 = isset($lines_2[$i]) ? $lines_2[$i] : null;
      
      if ($line_1 === null && $line_2 !== null) {
        // Line added
        $diff_lines[] = array(
          'type' => 'added',
          'line_number' => $i + 1,
          'content' => $line_2
        );
        $added_count++;
      } elseif ($line_1 !== null && $line_2 === null) {
        // Line removed
        $diff_lines[] = array(
          'type' => 'removed',
          'line_number' => $i + 1,
          'content' => $line_1
        );
        $removed_count++;
      } elseif ($line_1 !== $line_2) {
        // Line changed
        $diff_lines[] = array(
          'type' => 'changed',
          'line_number' => $i + 1,
          'old_content' => $line_1,
          'new_content' => $line_2
        );
        $changed_count++;
      } else {
        // Line unchanged
        $diff_lines[] = array(
          'type' => 'unchanged',
          'line_number' => $i + 1,
          'content' => $line_1
        );
      }
    }
    
    return array(
      'lines' => $diff_lines,
      'added_count' => $added_count,
      'removed_count' => $removed_count,
      'changed_count' => $changed_count
    );
  }
  
  /**
   * Restore a specific revision
   *
   * @param int $post_id Post ID
   * @param int $revision_id Revision ID to restore
   * @return bool|WP_Error True on success, WP_Error on failure
   */
  public function restore_revision($post_id, $revision_id) {
    $markdown = $this->storage->get_revision($revision_id);
    
    if (!$markdown) {
      return new WP_Error('revision_not_found', __('Revision not found', 'wpfm'));
    }
    
    // Save current state as a backup before restoring
    $current_markdown = $this->storage->get_latest_markdown($post_id);
    if ($current_markdown) {
      $this->storage->save_markdown($post_id, $current_markdown, 'backup');
    }
    
    // Save the restored revision as a new manual revision
    $new_revision_id = $this->storage->save_markdown($post_id, $markdown, 'restored');
    
    if (!$new_revision_id) {
      return new WP_Error('restore_failed', __('Failed to restore revision', 'wpfm'));
    }
    
    // Optionally update the post content with converted blocks
    $storage_mode = get_option('wpfm_storage_mode', 'dual');
    if ($storage_mode !== 'markdown_only') {
      $converter = new WPFM_Converter();
      $blocks = $converter->markdown_to_blocks($markdown);
      
      $result = wp_update_post(array(
        'ID' => $post_id,
        'post_content' => $blocks
      ));
      
      if (is_wp_error($result)) {
        return $result;
      }
    }
    
    return true;
  }
  
  /**
   * Get revision history with metadata
   *
   * @param int $post_id Post ID
   * @param int $limit Number of revisions to retrieve
   * @return array Revision history
   */
  public function get_revision_history($post_id, $limit = 20) {
    global $wpdb;
    
    $revisions = $wpdb->get_results($wpdb->prepare(
      "SELECT r.*, u.display_name as author_name
       FROM {$wpdb->prefix}wpfm_content r
       LEFT JOIN {$wpdb->users} u ON r.created_by = u.ID
       WHERE r.post_id = %d 
       ORDER BY r.created_at DESC 
       LIMIT %d",
      $post_id,
      $limit
    ));
    
    // Add additional metadata
    foreach ($revisions as &$revision) {
      $revision->time_ago = human_time_diff(strtotime($revision->created_at), current_time('timestamp'));
      $revision->content_length = strlen($revision->markdown_content);
      $revision->word_count = str_word_count(strip_tags($revision->markdown_content));
    }
    
    return $revisions;
  }
  
  /**
   * Clean up old revisions based on settings
   *
   * @param int $post_id Post ID (optional, cleans all posts if not provided)
   * @return int Number of revisions cleaned up
   */
  public function cleanup_revisions($post_id = null) {
    global $wpdb;
    
    $keep_count = apply_filters('wpfm_revision_keep_count', 50);
    $keep_days = apply_filters('wpfm_revision_keep_days', 365);
    
    $where_clause = '';
    $params = array($keep_count);
    
    if ($post_id) {
      $where_clause = 'AND post_id = %d';
      $params[] = $post_id;
    }
    
    // Keep recent revisions and delete old ones
    $deleted = $wpdb->query($wpdb->prepare(
      "DELETE FROM {$wpdb->prefix}wpfm_content 
       WHERE created_at < DATE_SUB(NOW(), INTERVAL %d DAY)
       AND id NOT IN (
         SELECT id FROM (
           SELECT id FROM {$wpdb->prefix}wpfm_content 
           WHERE 1=1 {$where_clause}
           ORDER BY created_at DESC 
           LIMIT %d
         ) as keep_revisions
       )",
      $keep_days,
      ...$params
    ));
    
    return $deleted;
  }
  
  /**
   * Export revision history for a post
   *
   * @param int $post_id Post ID
   * @param string $format Export format (json, csv)
   * @return string|WP_Error Export data or error
   */
  public function export_revisions($post_id, $format = 'json') {
    $revisions = $this->get_revision_history($post_id, 0); // Get all revisions
    
    if (empty($revisions)) {
      return new WP_Error('no_revisions', __('No revisions found for this post', 'wpfm'));
    }
    
    $export_data = array(
      'post_id' => $post_id,
      'export_date' => current_time('mysql'),
      'total_revisions' => count($revisions),
      'revisions' => $revisions
    );
    
    switch ($format) {
      case 'json':
        return wp_json_encode($export_data, JSON_PRETTY_PRINT);
        
      case 'csv':
        $csv_data = "ID,Post ID,Revision Type,Created At,Created By,Author Name,Content Length,Word Count\n";
        foreach ($revisions as $revision) {
          $csv_data .= sprintf(
            "%d,%d,%s,%s,%d,%s,%d,%d\n",
            $revision->id,
            $revision->post_id,
            $revision->revision_type,
            $revision->created_at,
            $revision->created_by,
            $revision->author_name ?: 'Unknown',
            $revision->content_length,
            $revision->word_count
          );
        }
        return $csv_data;
        
      default:
        return new WP_Error('invalid_format', __('Invalid export format', 'wpfm'));
    }
  }
  
  /**
   * AJAX handler for comparing revisions
   *
   * @return void
   */
  public function ajax_compare_revisions() {
    check_ajax_referer('wpfm_nonce', 'nonce');
    
    $revision_id_1 = intval($_POST['revision_id_1']);
    $revision_id_2 = intval($_POST['revision_id_2']);
    
    if (!current_user_can('edit_posts')) {
      wp_send_json_error(__('Permission denied', 'wpfm'));
    }
    
    $comparison = $this->compare_revisions($revision_id_1, $revision_id_2);
    
    if (isset($comparison['error'])) {
      wp_send_json_error($comparison['error']);
    }
    
    wp_send_json_success($comparison);
  }
  
  /**
   * AJAX handler for restoring a revision
   *
   * @return void
   */
  public function ajax_restore_revision() {
    check_ajax_referer('wpfm_nonce', 'nonce');
    
    $post_id = intval($_POST['post_id']);
    $revision_id = intval($_POST['revision_id']);
    
    if (!current_user_can('edit_post', $post_id)) {
      wp_send_json_error(__('Permission denied', 'wpfm'));
    }
    
    $result = $this->restore_revision($post_id, $revision_id);
    
    if (is_wp_error($result)) {
      wp_send_json_error($result->get_error_message());
    }
    
    wp_send_json_success(array(
      'message' => __('Revision restored successfully', 'wpfm')
    ));
  }
  
  /**
   * AJAX handler for deleting a revision
   *
   * @return void
   */
  public function ajax_delete_revision() {
    check_ajax_referer('wpfm_nonce', 'nonce');
    
    $revision_id = intval($_POST['revision_id']);
    
    if (!current_user_can('edit_posts')) {
      wp_send_json_error(__('Permission denied', 'wpfm'));
    }
    
    $result = $this->storage->delete_revision($revision_id);
    
    if (!$result) {
      wp_send_json_error(__('Failed to delete revision', 'wpfm'));
    }
    
    wp_send_json_success(array(
      'message' => __('Revision deleted successfully', 'wpfm')
    ));
  }
}
?>
