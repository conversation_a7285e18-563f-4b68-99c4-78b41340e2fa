<?php
/**
 * WPFM Editor Class
 * Handles the markdown editor interface and functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
  exit;
}

class WPFM_Editor {
  
  private $converter;
  private $storage;
  
  public function __construct() {
    $this->converter = new WPFM_Converter();
    $this->storage = new WPFM_Storage();
    
    $this->init_hooks();
  }
  
  /**
   * Initialize editor hooks
   *
   * @return void
   */
  private function init_hooks() {
    add_action('enqueue_block_editor_assets', array($this, 'enqueue_editor_assets'));
    add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
    add_action('wp_ajax_wpfm_preview_markdown', array($this, 'ajax_preview_markdown'));
    add_action('wp_ajax_wpfm_validate_markdown', array($this, 'ajax_validate_markdown'));
  }
  
  /**
   * Enqueue editor assets for the block editor
   *
   * @return void
   */
  public function enqueue_editor_assets() {
    // Check if we should load on this post type
    global $post;
    if (!$this->should_load_editor($post)) {
      return;
    }
    
    wp_enqueue_script(
      'wpfm-block-editor',
      WPFM_PLUGIN_URL . 'assets/js/block-editor.js',
      array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-data'),
      WPFM_VERSION,
      true
    );
    
    wp_enqueue_style(
      'wpfm-block-editor',
      WPFM_PLUGIN_URL . 'assets/css/block-editor.css',
      array('wp-edit-blocks'),
      WPFM_VERSION
    );
    
    wp_localize_script('wpfm-block-editor', 'wpfm_editor', array(
      'ajax_url' => admin_url('admin-ajax.php'),
      'nonce' => wp_create_nonce('wpfm_editor_nonce'),
      'post_id' => $post ? $post->ID : 0,
      'enabled_post_types' => get_option('wpfm_enabled_post_types', array('post', 'page')),
      'storage_mode' => get_option('wpfm_storage_mode', 'dual')
    ));
  }
  
  /**
   * Enqueue admin assets for post edit screens
   *
   * @param string $hook Current admin page hook
   * @return void
   */
  public function enqueue_admin_assets($hook) {
    if (!in_array($hook, array('post.php', 'post-new.php'))) {
      return;
    }
    
    global $post;
    if (!$this->should_load_editor($post)) {
      return;
    }
    
    wp_enqueue_script(
      'wpfm-admin-editor',
      WPFM_PLUGIN_URL . 'assets/js/admin-editor.js',
      array('jquery', 'wp-util'),
      WPFM_VERSION,
      true
    );
    
    wp_enqueue_style(
      'wpfm-admin-editor',
      WPFM_PLUGIN_URL . 'assets/css/admin-editor.css',
      array(),
      WPFM_VERSION
    );
    
    // Include CodeMirror for syntax highlighting
    wp_enqueue_code_editor(array('type' => 'text/markdown'));
    
    wp_localize_script('wpfm-admin-editor', 'wpfm_admin', array(
      'ajax_url' => admin_url('admin-ajax.php'),
      'nonce' => wp_create_nonce('wpfm_editor_nonce'),
      'post_id' => $post ? $post->ID : 0,
      'strings' => array(
        'save_success' => __('Markdown saved successfully', 'wpfm'),
        'save_error' => __('Error saving markdown', 'wpfm'),
        'preview_error' => __('Error generating preview', 'wpfm'),
        'sync_success' => __('Content synced successfully', 'wpfm'),
        'sync_error' => __('Error syncing content', 'wpfm'),
        'confirm_load_revision' => __('Are you sure you want to load this revision? Unsaved changes will be lost.', 'wpfm')
      )
    ));
  }
  
  /**
   * Check if the editor should be loaded for the current post
   *
   * @param WP_Post|null $post Post object
   * @return bool True if editor should be loaded
   */
  private function should_load_editor($post) {
    if (!$post) {
      return false;
    }
    
    $enabled_types = get_option('wpfm_enabled_post_types', array('post', 'page'));
    return in_array($post->post_type, $enabled_types);
  }
  
  /**
   * Render the markdown editor interface
   *
   * @param WP_Post $post Post object
   * @return void
   */
  public function render_editor_interface($post) {
    $markdown = $this->storage->get_latest_markdown($post->ID);
    $revisions = $this->storage->get_revisions($post->ID, 10);
    $stats = $this->storage->get_revision_stats($post->ID);
    
    ?>
    <div id="wpfm-editor-container" class="wpfm-editor-container">
      <div class="wpfm-editor-header">
        <div class="wpfm-toolbar">
          <div class="wpfm-toolbar-group">
            <button type="button" class="button wpfm-btn" id="wpfm-toggle-preview" data-mode="preview">
              <span class="dashicons dashicons-visibility"></span>
              <?php _e('Preview', 'wpfm'); ?>
            </button>
            <button type="button" class="button wpfm-btn" id="wpfm-sync-from-blocks">
              <span class="dashicons dashicons-update"></span>
              <?php _e('Sync from Blocks', 'wpfm'); ?>
            </button>
            <button type="button" class="button wpfm-btn" id="wpfm-sync-to-blocks">
              <span class="dashicons dashicons-upload"></span>
              <?php _e('Sync to Blocks', 'wpfm'); ?>
            </button>
          </div>
          
          <div class="wpfm-toolbar-group">
            <button type="button" class="button button-primary wpfm-btn" id="wpfm-save-markdown">
              <span class="dashicons dashicons-yes"></span>
              <?php _e('Save Draft', 'wpfm'); ?>
            </button>
          </div>
        </div>
        
        <div class="wpfm-editor-tabs">
          <button class="wpfm-tab wpfm-tab-active" data-tab="markdown">
            <span class="dashicons dashicons-edit"></span>
            <?php _e('Markdown', 'wpfm'); ?>
          </button>
          <button class="wpfm-tab" data-tab="preview">
            <span class="dashicons dashicons-visibility"></span>
            <?php _e('Preview', 'wpfm'); ?>
          </button>
          <button class="wpfm-tab" data-tab="blocks">
            <span class="dashicons dashicons-screenoptions"></span>
            <?php _e('Block View', 'wpfm'); ?>
          </button>
        </div>
      </div>
      
      <div class="wpfm-editor-content">
        <div id="wpfm-markdown-pane" class="wpfm-editor-pane wpfm-pane-active">
          <textarea 
            name="wpfm_markdown" 
            id="wpfm-markdown-content" 
            class="wpfm-markdown-textarea"
            placeholder="<?php esc_attr_e('Enter your markdown content here...', 'wpfm'); ?>"
          ><?php echo esc_textarea($markdown); ?></textarea>
        </div>
        
        <div id="wpfm-preview-pane" class="wpfm-editor-pane">
          <div id="wpfm-preview-content" class="wpfm-preview-content">
            <div class="wpfm-preview-placeholder">
              <span class="dashicons dashicons-visibility"></span>
              <p><?php _e('Click "Preview" to see how your markdown will look.', 'wpfm'); ?></p>
            </div>
          </div>
        </div>
        
        <div id="wpfm-blocks-pane" class="wpfm-editor-pane">
          <div id="wpfm-blocks-content" class="wpfm-blocks-content">
            <div class="wpfm-blocks-placeholder">
              <span class="dashicons dashicons-screenoptions"></span>
              <p><?php _e('Block representation will appear here.', 'wpfm'); ?></p>
            </div>
          </div>
        </div>
      </div>
      
      <div class="wpfm-editor-footer">
        <div class="wpfm-editor-stats">
          <span class="wpfm-stat">
            <strong><?php _e('Revisions:', 'wpfm'); ?></strong> 
            <?php echo intval($stats['total_revisions']); ?>
          </span>
          <?php if ($stats['last_modified']): ?>
          <span class="wpfm-stat">
            <strong><?php _e('Last Modified:', 'wpfm'); ?></strong> 
            <?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($stats['last_modified'])); ?>
          </span>
          <?php endif; ?>
        </div>
        
        <div class="wpfm-editor-actions">
          <button type="button" class="button wpfm-btn" id="wpfm-show-revisions">
            <span class="dashicons dashicons-backup"></span>
            <?php _e('View Revisions', 'wpfm'); ?>
          </button>
        </div>
      </div>
    </div>
    
    <!-- Revisions Modal -->
    <div id="wpfm-revisions-modal" class="wpfm-modal" style="display: none;">
      <div class="wpfm-modal-content">
        <div class="wpfm-modal-header">
          <h3><?php _e('Markdown Revisions', 'wpfm'); ?></h3>
          <button type="button" class="wpfm-modal-close">&times;</button>
        </div>
        
        <div class="wpfm-modal-body">
          <div id="wpfm-revisions-list" class="wpfm-revisions-list">
            <?php if (empty($revisions)): ?>
              <p class="wpfm-no-revisions"><?php _e('No revisions found.', 'wpfm'); ?></p>
            <?php else: ?>
              <ul class="wpfm-revision-items">
                <?php foreach ($revisions as $revision): ?>
                  <li class="wpfm-revision-item">
                    <div class="wpfm-revision-info">
                      <strong class="wpfm-revision-date">
                        <?php echo date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($revision->created_at)); ?>
                      </strong>
                      <span class="wpfm-revision-type wpfm-revision-type-<?php echo esc_attr($revision->revision_type); ?>">
                        <?php echo esc_html(ucfirst($revision->revision_type)); ?>
                      </span>
                      <span class="wpfm-revision-author">
                        <?php 
                        $user = get_user_by('id', $revision->created_by);
                        echo $user ? esc_html($user->display_name) : __('Unknown', 'wpfm');
                        ?>
                      </span>
                    </div>
                    <div class="wpfm-revision-actions">
                      <button type="button" class="button button-small wpfm-load-revision" data-revision-id="<?php echo intval($revision->id); ?>">
                        <?php _e('Load', 'wpfm'); ?>
                      </button>
                      <button type="button" class="button button-small wpfm-preview-revision" data-revision-id="<?php echo intval($revision->id); ?>">
                        <?php _e('Preview', 'wpfm'); ?>
                      </button>
                    </div>
                  </li>
                <?php endforeach; ?>
              </ul>
            <?php endif; ?>
          </div>
        </div>
      </div>
    </div>
    
    <style>
    .wpfm-editor-container {
      border: 1px solid #ddd;
      border-radius: 4px;
      background: #fff;
      margin: 20px 0;
    }
    
    .wpfm-editor-header {
      border-bottom: 1px solid #ddd;
      background: #f9f9f9;
    }
    
    .wpfm-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      border-bottom: 1px solid #ddd;
    }
    
    .wpfm-toolbar-group {
      display: flex;
      gap: 8px;
    }
    
    .wpfm-btn {
      display: inline-flex;
      align-items: center;
      gap: 5px;
    }
    
    .wpfm-editor-tabs {
      display: flex;
      background: #f9f9f9;
    }
    
    .wpfm-tab {
      padding: 10px 15px;
      border: none;
      background: transparent;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      display: flex;
      align-items: center;
      gap: 5px;
    }
    
    .wpfm-tab:hover {
      background: #f0f0f0;
    }
    
    .wpfm-tab-active {
      background: #fff;
      border-bottom-color: #0073aa;
    }
    
    .wpfm-editor-content {
      position: relative;
      min-height: 400px;
    }
    
    .wpfm-editor-pane {
      display: none;
      padding: 15px;
      min-height: 400px;
    }
    
    .wpfm-pane-active {
      display: block;
    }
    
    .wpfm-markdown-textarea {
      width: 100%;
      min-height: 400px;
      font-family: Monaco, Consolas, monospace;
      font-size: 14px;
      line-height: 1.5;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      resize: vertical;
    }
    
    .wpfm-preview-content,
    .wpfm-blocks-content {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      min-height: 400px;
      background: #fff;
    }
    
    .wpfm-preview-placeholder,
    .wpfm-blocks-placeholder {
      text-align: center;
      color: #666;
      padding: 50px 20px;
    }
    
    .wpfm-preview-placeholder .dashicons,
    .wpfm-blocks-placeholder .dashicons {
      font-size: 48px;
      opacity: 0.3;
      display: block;
      margin-bottom: 10px;
    }
    
    .wpfm-editor-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      border-top: 1px solid #ddd;
      background: #f9f9f9;
      font-size: 12px;
    }
    
    .wpfm-editor-stats {
      display: flex;
      gap: 20px;
    }
    
    .wpfm-stat {
      color: #666;
    }
    
    /* Modal Styles */
    .wpfm-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 100000;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .wpfm-modal-content {
      background: #fff;
      border-radius: 4px;
      width: 90%;
      max-width: 600px;
      max-height: 80vh;
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    
    .wpfm-modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid #ddd;
      background: #f9f9f9;
    }
    
    .wpfm-modal-header h3 {
      margin: 0;
    }
    
    .wpfm-modal-close {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .wpfm-modal-body {
      padding: 20px;
      overflow-y: auto;
      flex: 1;
    }
    
    .wpfm-revision-items {
      list-style: none;
      margin: 0;
      padding: 0;
    }
    
    .wpfm-revision-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #eee;
    }
    
    .wpfm-revision-item:last-child {
      border-bottom: none;
    }
    
    .wpfm-revision-info {
      flex: 1;
    }
    
    .wpfm-revision-date {
      display: block;
      margin-bottom: 5px;
    }
    
    .wpfm-revision-type {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 11px;
      text-transform: uppercase;
      margin-right: 8px;
    }
    
    .wpfm-revision-type-manual {
      background: #0073aa;
      color: #fff;
    }
    
    .wpfm-revision-type-auto {
      background: #00a32a;
      color: #fff;
    }
    
    .wpfm-revision-type-converted {
      background: #dba617;
      color: #fff;
    }
    
    .wpfm-revision-author {
      color: #666;
      font-size: 12px;
    }
    
    .wpfm-revision-actions {
      display: flex;
      gap: 5px;
    }
    
    .wpfm-no-revisions {
      text-align: center;
      color: #666;
      font-style: italic;
      padding: 40px 20px;
    }
    </style>
    <?php
  }
  
  /**
   * AJAX handler for markdown preview
   *
   * @return void
   */
  public function ajax_preview_markdown() {
    check_ajax_referer('wpfm_editor_nonce', 'nonce');
    
    $post_id = intval($_POST['post_id']);
    $markdown = wp_unslash($_POST['markdown']);
    
    if (!current_user_can('edit_post', $post_id)) {
      wp_send_json_error(__('Permission denied', 'wpfm'));
    }
    
    // Convert markdown to blocks and then render
    $blocks = $this->converter->markdown_to_blocks($markdown);
    $rendered = do_blocks($blocks);
    
    wp_send_json_success(array(
      'html' => $rendered
    ));
  }
  
  /**
   * AJAX handler for markdown validation
   *
   * @return void
   */
  public function ajax_validate_markdown() {
    check_ajax_referer('wpfm_editor_nonce', 'nonce');
    
    $markdown = wp_unslash($_POST['markdown']);
    
    // Basic validation
    $errors = array();
    $warnings = array();
    
    // Check for common markdown issues
    if (preg_match('/^#{7,}/', $markdown)) {
      $errors[] = __('Headers cannot be more than 6 levels deep', 'wpfm');
    }
    
    if (preg_match('/!\[.*\]\([^)]*\s[^)]*\)/', $markdown)) {
      $warnings[] = __('Image URLs should not contain spaces', 'wpfm');
    }
    
    wp_send_json_success(array(
      'errors' => $errors,
      'warnings' => $warnings,
      'valid' => empty($errors)
    ));
  }
}
?>
